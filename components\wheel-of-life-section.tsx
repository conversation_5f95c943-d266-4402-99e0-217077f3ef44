"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";

export function WheelOfLifeSection() {
  return (
    <section className="py-20 max-md:py-10 bg-landingBackground">
      <div className="container max-md:px-4">
        <div className="flex justify-between max-md:flex-col max-md:px-4">
          <div className="max-w-[760px] max-md:text-center">
            <h2 className="max-md:text-4xl mb-6 text-display-lg text-paragraphContent tracking-tight">
              The Wheel of Life Assessment.
            </h2>
            <p className="text-paragraphContent mb-6 text-body-lg max-w-[726px]">
              The Wheel of Life is a coaching tool designed to help you
              objectively evaluate key areas of your life—such as career,
              health, relationships, finances, personal development, and
              leisure. Imagine your life as a wheel with various segments, each
              contributing to your overall balance, much like a tire needs to be
              smooth for an optimal ride.
            </p>
            <p className="text-paragraphContent mb-6 text-body-lg max-w-[726px]">
              This assessment provides a clear, holistic view of your current
              state, allowing you to identify areas that require improvement. By
              pinpointing these areas, you can set focused priorities and define
              actionable goals, ultimately bridging the gap between your current
              situation and the life you aspire to lead.
            </p>
            <div className="mt-8">
              <Button
                variant="primary"
                className="text-title-medium text-lightBlue px-6"
              >
                Take the 5-Minute Assessment
              </Button>
            </div>
          </div>
          <Image
            src="/landing/chart.svg"
            alt="Wheel of Life Diagram"
            width={460}
            height={460}
            className="max-w-full h-auto max-md:mt-6"
          />
        </div>
      </div>
    </section>
  );
}
