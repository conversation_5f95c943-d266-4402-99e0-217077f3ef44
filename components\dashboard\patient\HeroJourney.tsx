import React from 'react';
import { useRouter } from 'next/navigation';
import { Star, Trophy, Calendar, Upload } from 'lucide-react';
import { it } from 'node:test';

// Mock data for the Hero's Journey
const journeyLevels = [
  { area: 'Finance', level: 6, color: '#00B052' },
  { area: 'Romance', level: 4, color: '#CC1F70' },
  { area: 'Family', level: 5, color: '#0391B6' },
  { area: 'Creative', level: 4, color: '#EEB122' },
  { area: 'Physical', level: 2, color: '#8BBB4B' },
  { area: 'Social', level: 4, color: '#EE5D60' },
  { area: 'Career', level: 7, color: '#365697' },
  { area: 'Emotions', level: 5, color: '#EA7E31' },
  { area: 'Spiritual', level: 4, color: '#633A86' },
];

const badges = [
  {
    title: 'Champion',
    subtitle: 'Complete 5 challenges',
    icon: '🏆',
    color: '#FFD700',
    progress: '3/5'
  },
  {
    title: 'Champion',
    subtitle: 'Complete 5 challenges',
    icon: '🏆',
    color: '#FFD700',
    progress: '5/5'
  },
  {
    title: 'Champion',
    subtitle: 'Complete 5 challenges',
    icon: '🏆',
    color: '#FFD700',
    progress: '2/5'
  },
  {
    title: 'Adventurer',
    subtitle: 'Complete 3 challenges',
    icon: '🎯',
    color: '#9333EA',
    progress: '3/3'
  }
];

const activePrograms = [
  {
    title: 'Welcome to Peakality',
    image: '/dashboard/driver.svg',
    progress: 60,
    challenges: 3,
    duration: '30 days',
    reward: '900 pts'
  }
];

const activeChallenges = [
  {
    title: 'Wheel of Life Challenge',
    image: '/dashboard/driver.svg',
    progress: 75,
    duration: '14 days',
    reward: '500 pts'
  }
];

export function HeroJourney() {
  const router = useRouter();
  
  return (
    <div className="w-full space-y-6">
      {/* Journey Levels */}
      <div className="grid grid-cols-3 sm:grid-cols-5 lg:grid-cols-9 gap-2 md:gap-4">
        {journeyLevels.map((item, index) => (
          <div key={index} className={`text-center bg-${item.color} border-${item.color}`}>
            <div className="text-xs md:text-sm text-gray-600 mb-1">{item.area}</div>
            <div
              className={`w-12 h-12 md:w-16 md:h-16 rounded-lg flex items-center justify-center text-${item.color} font-bold text-sm md:text-base mx-auto`}
            >
              Level {item.level}
            </div>
          </div>
        ))}
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        {/* My Streak */}
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-bold text-gray-900 mb-2">My Streak</h3>
          <div className="flex items-center gap-2">
            <Star className="w-6 h-6 text-orange-500" />
            <span className="text-2xl font-bold text-gray-900">8 Days</span>
          </div>
          <button className="text-sm text-blue-600 hover:underline mt-2">Check in</button>
        </div>

        {/* My Points */}
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-bold text-gray-900 mb-2">My Points</h3>
          <div className="text-2xl font-bold text-gray-900">2,500</div>
        </div>

        {/* My Rank */}
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-bold text-gray-900 mb-2">My Rank</h3>
          <div className="text-2xl font-bold text-gray-900">325</div>
        </div>
      </div>

      {/* Streak Calendar and Badges Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Streak Calendar */}
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Streak Calendar</h3>
          <div className="text-center text-sm text-gray-600 mb-2">April 2024</div>
          <div className="grid grid-cols-7 gap-1 text-xs text-center">
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
              <div key={day} className="p-1 text-gray-500">{day}</div>
            ))}
            {Array.from({ length: 30 }, (_, i) => (
              <div key={i} className="w-6 h-6 flex items-center justify-center">
                {i < 8 ? (
                  <Star className="w-4 h-4 text-orange-500" />
                ) : (
                  <span className="text-gray-400">{i + 1}</span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* My Badges */}
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-bold text-gray-900">My Badges</h3>
            <button className="text-sm text-blue-600 hover:underline">View All Badges</button>
          </div>
          <div className="grid grid-cols-2 gap-3">
            {badges.map((badge, index) => (
              <div key={index} className="flex items-center gap-3 p-2 rounded-lg border">
                <div className="text-2xl">{badge.icon}</div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-semibold text-gray-900 truncate">{badge.title}</div>
                  <div className="text-xs text-gray-600 truncate">{badge.subtitle}</div>
                  <div className="text-xs text-gray-500">{badge.progress}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Programs and Challenges Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* My Active Programs */}
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-bold text-gray-900 mb-4">My Active Programs</h3>
          {activePrograms.map((program, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex gap-4">
                <img
                  src={program.image}
                  alt={program.title}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">{program.title}</h4>
                  <div className="text-xs text-gray-600 mb-2">
                    Challenges: {program.challenges} • Duration: {program.duration} • Reward: {program.reward}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${program.progress}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-600 mt-1">{program.progress}%</div>
                </div>
              </div>
              <button className="w-full mt-3 bg-gray-600 text-white py-2 px-4 rounded text-sm hover:bg-gray-700">
                View
              </button>
            </div>
          ))}
        </div>

        {/* My Active Challenges */}
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-bold text-gray-900 mb-4">My Active Challenges</h3>
          {activeChallenges.map((challenge, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex gap-4">
                <img
                  src={challenge.image}
                  alt={challenge.title}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">{challenge.title}</h4>
                  <div className="text-xs text-gray-600 mb-2">
                    Duration: {challenge.duration} • Reward: {challenge.reward}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${challenge.progress}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-600 mt-1">{challenge.progress}%</div>
                </div>
              </div>
              <button className="w-full mt-3 bg-gray-600 text-white py-2 px-4 rounded text-sm hover:bg-gray-700">
                Resume
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
