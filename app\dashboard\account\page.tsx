"use client";
import { useState } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { CopyCard, UserAvatar, ViewEyeBtn } from "@/utils/icons";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";

const initialProfile = {
  name: "<PERSON>",
  username: "@lizthetherapist",
  state: "Florida",
  org: "White Sands",
  email: "<EMAIL>",
  phone: "+****************",
  photo: "",
};

const card = {
  name: "ADRIAN TRA",
  number: "4889 9271 1937 1932",
  expiry: "12/28",
  cvv: "***",
};

export default function AccountPage() {
  const [tab, setTab] = useState<"profile" | "billing" | "notifications">(
    "profile"
  );
  const [profile, setProfile] = useState(initialProfile);
  const [editProfile, setEditProfile] = useState(initialProfile);
  const [photoPreview, setPhotoPreview] = useState<string>(profile.photo);
  const [isDirty, setIsDirty] = useState(false);

  // Handle photo upload
  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPhotoPreview(reader.result as string);
        setEditProfile((prev) => ({ ...prev, photo: reader.result as string }));
        setIsDirty(true);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove photo
  const handleRemovePhoto = () => {
    setPhotoPreview("");
    setEditProfile((prev) => ({ ...prev, photo: "" }));
    setIsDirty(true);
  };

  // Handle profile field change
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setEditProfile((prev) => ({ ...prev, [name]: value }));
    setIsDirty(true);
  };

  // Save profile
  const handleSave = () => {
    setProfile(editProfile);
    setIsDirty(false);
  };

  return (
    <div className="flex flex-col">
      <p className="text-display-md text-paragraphContent mb-6">My Account</p>
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Tabs */}
        <div className="w-full lg:w-auto">
          <div className="bg-white rounded border p-0.5 w-[220px] max-md:w-full">
            <button
              className={`flex-1 block w-full text-left px-4 py-2 sm:py-3 rounded font-medium text-sm sm:text-base text-title-medium ${
                tab === "profile"
                  ? "bg-landingBackground text-[#21272A]"
                  : "hover:bg-gray-50 text-gray-700"
              }`}
              onClick={() => setTab("profile")}
            >
              Profile
            </button>
            <button
              className={`flex-1 block w-full text-left px-4 py-2 sm:py-3 rounded font-medium text-sm sm:text-base text-title-medium ${
                tab === "billing"
                  ? "bg-landingBackground text-[#21272A]"
                  : "hover:bg-gray-50 text-gray-700"
              }`}
              onClick={() => setTab("billing")}
            >
              Billing
            </button>
            <button
              className={`flex-1 block w-full text-left px-4 py-2 sm:py-3 rounded font-medium text-sm sm:text-base text-title-medium ${
                tab === "notifications"
                  ? "bg-landingBackground text-[#21272A]"
                  : "hover:bg-gray-50 text-gray-700"
              }`}
              onClick={() => setTab("notifications")}
            >
              Notifications
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="flex-1 px-4">
          {/* Profile Tab */}
          {tab === "profile" && (
            <div className="rounded max-md:px-0 w-[597px] max-md:w-full">
              <div className="flex flex-col gap-6 ">
                {/* Profile Photo Card */}
                <div className="bg-white border rounded p-6 max-md:px-0 mb-2">
                  <div className="flex justify-around items-center">
                    {/* Left: Avatar + Upload/Remove */}
                    <div className="flex gap-4 sm:gap-6 md:w-auto flex-col">
                      <div className="text-body-large font-bold max-md:text-center">
                        Profile Photo
                      </div>
                      <div className="flex items-center max-md:flex-col">
                        <div className="w-20 h-20 sm:w-[96px] sm:h-[96px] rounded-full bg-landingBackground flex items-center justify-center text-3xl sm:text-4xl text-gray-300 mb-2 overflow-hidden border border-gray-200">
                          {photoPreview ? (
                            <Image
                              src={photoPreview}
                              alt="Profile"
                              width={96}
                              height={96}
                              className="rounded-full object-cover w-full h-full"
                            />
                          ) : (
                            <UserAvatar />
                          )}
                        </div>
                        <div className="text-center ml-4 sm:ml-8">
                          <label
                            className="mb-1 w-full"
                            htmlFor="photo-upload"
                          >
                            <span className="flex items-center justify-center w-full px-3 sm:px-4 py-2 text-xs sm:text-sm border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded cursor-pointer transition">
                              + Upload Photo
                            </span>
                            <input
                              id="photo-upload"
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={handlePhotoChange}
                            />
                          </label>
                          <button
                            className="text-blue-700 text-xs underline mt-1"
                            type="button"
                            onClick={handleRemovePhoto}
                          >
                            remove
                          </button>
                        </div>
                      </div>
                    </div>
                    {/* Divider */}
                    <div className="hidden md:block w-px bg-darkBlueBackground h-36" />
                    {/* Right: Image requirements */}
                    <div className="mt-4 md:mt-0 max-md:text-center text-paragraphContent">
                      <div className="text-body-lg">Image requirements:</div>
                      <ul className="text-body-small mt-4 flex flex-col gap-2">
                        <li>Min. 400 x 400px</li>
                        <li>Max. 2MB</li>
                        <li>Your face or company logo</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Profile Details Card */}
                <div className="bg-white border rounded p-6 text-paragraphContent">
                  <div className="text-body-large mb-4 font-bold">
                    Profile Details
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    {/* Full Name */}
                    <div>
                      <label className="text-label-large mb-1">
                        Full Name
                      </label>
                      <Input
                        className="w-full bg-landingBackground"
                        name="name"
                        value={editProfile.name}
                        onChange={handleChange}
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        We won't show your full name publicly.
                      </div>
                    </div>
                    {/* Username */}
                    <div>
                      <label className="text-label-large mb-1">
                        Username
                      </label>
                      <Input
                        className="w-full bg-landingBackground"
                        name="username"
                        value={editProfile.username}
                        onChange={handleChange}
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        This will be public. You can change it anytime.
                      </div>
                    </div>
                    {/* Licensed State */}
                    <div>
                      <label className="text-label-large mb-1">
                        Licensed State
                      </label>
                      <select
                        className="w-full p-2 rounded bg-landingBackground h-12"
                        name="state"
                        value={editProfile.state}
                        onChange={handleChange}
                      >
                        <option>Florida</option>
                        <option>California</option>
                        <option>New York</option>
                      </select>
                    </div>
                    {/* Affiliated Organization */}
                    <div>
                      <label className="text-label-large mb-1">
                        Affiliated Organization
                      </label>
                      <Input
                        className="w-full bg-landingBackground"
                        name="org"
                        value={editProfile.org}
                        onChange={handleChange}
                      />
                    </div>
                    {/* Email Address */}
                    <div>
                      <label className="text-label-large mb-1">
                        Email Address
                      </label>
                      <Input
                        className="w-full bg-landingBackground"
                        name="email"
                        value={editProfile.email}
                        onChange={handleChange}
                      />
                    </div>
                    {/* Phone Number */}
                    <div>
                      <label className="text-label-large mb-1">
                        Phone Number
                      </label>
                      <Input
                        className="w-full bg-landingBackground"
                        name="phone"
                        value={editProfile.phone}
                        onChange={handleChange}
                      />
                    </div>
                  </div>
                  <div className="flex justify-end mt-6">
                    <Button
                      className={`w-full sm:w-auto bg-[#556080] text-white px-8 py-2 rounded font-medium shadow-none ${
                        !isDirty ? "opacity-50 cursor-not-allowed" : ""
                      }`}
                      type="button"
                      onClick={handleSave}
                      disabled={!isDirty}
                    >
                      Save
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Billing Tab */}
          {tab === "billing" && (
            <div className="rounded w-[504px] max-md:w-full">
              <div>
                <div className="flex border bg-lightBlue px-5 py-3 justify-between mb-6 gap-2">
                  <div className="text-body-large text-netural100 font-bold">
                    Primary Card
                  </div>
                  <button className="text-gray-500 hover:text-gray-900 flex items-center gap-1 text-sm">
                    <span>›</span>
                  </button>
                </div>
                <div className="border rounded-lg p-5 bg-netural20">
                  <div className="flex justify-center">
                    <Image
                      src="/dashboard/card.png"
                      alt="Card"
                      width={382}
                      height={180}
                      priority
                      className="rounded-xl object-cover"
                    />
                  </div>
                  <div className="mt-2 px-6">
                    <div className="mb-2 text-netural100 text-body-lg">
                      Card Information
                    </div>
                    <div className="text-body-large text-netural90">
                      <div className="flex justify-between mb-2">
                        <span>Card No.</span>
                        <div className="flex items-center gap-2 justify-end">
                          <button
                            type="button"
                            tabIndex={-1}
                            aria-label="Copy card number"
                          >
                            <CopyCard />
                          </button>
                          <span className="tracking-wider">
                            {card.number}
                          </span>
                        </div>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span>Expiry date</span>
                        <div className="flex items-center gap-2 justify-end">
                          <span>{card.expiry}</span>
                        </div>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span>CVV (3-digit security code)</span>
                        <div className="flex items-center gap-2 justify-end">
                          <ViewEyeBtn />
                          <span>{card.cvv}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notifications Tab */}
          {tab === "notifications" && (
            <div className="bg-white rounded border w-[504px] max-md:w-full p-8">
              <div className="text-title-lg text-netural100 font-bold mb-4">
                Desktop notifications
              </div>
              <div className="mb-4">
                <label className="flex items-center gap-2 text-body-large text-netural90">
                  <input type="checkbox" defaultChecked />
                  <span>Send notifications to my computer</span>
                </label>
              </div>
              <div className="mb-6 ml-8 max-md:ml-0">
                <div className="font-medium mb-2 text-body-large text-netural100">File comments</div>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input
                    type="radio"
                    name="desktop-file-comments"
                    defaultChecked
                  />
                  <span>All comments, mentions, and replies</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="desktop-file-comments" />
                  <span>Only mentions and replies</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="desktop-file-comments" />
                  <span>None</span>
                </label>
              </div>
              <div className="text-title-lg text-netural100 font-bold mb-4">
                Email notifications
              </div>
              <div className="mb-4">
                <label className="flex items-center gap-2 text-body-large text-netural90">
                  <input type="checkbox" defaultChecked />
                  <span>Send notifications by email</span>
                </label>
                <div className="text-body-small text-netural40 ml-6">
                  You'll still get other emails from Peakality, like important account or billing info.
                </div>
              </div>
              <div className="mb-6 ml-8 max-md:ml-0">
                <div className="font-medium mb-2 text-body-large text-netural100">File comments</div>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input
                    type="radio"
                    name="email-file-comments"
                    defaultChecked
                  />
                  <span>All comments, mentions, and replies</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-file-comments" />
                  <span>Only mentions and replies</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-file-comments" />
                  <span>None</span>
                </label>
              </div>
              <div className="mb-6 ml-8 max-md:ml-0">
                <div className="font-medium mb-2 text-body-large text-netural100">Invites and requests</div>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-invites" defaultChecked />
                  <span>All types of invites and requests</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-invites" />
                  <span>Only invites and requests that need my response</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-invites" />
                  <span>None</span>
                </label>
              </div>
              <div className="mb-6 ml-8 max-md:ml-0">
                <div className="font-medium mb-2 text-body-large text-netural100">Community comments</div>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-community" defaultChecked />
                  <span>All comments on my published resources</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-community" />
                  <span>Only mentions and replies</span>
                </label>
                <label className="flex items-center gap-2 mb-1 text-body-large text-netural90">
                  <input type="radio" name="email-community" />
                  <span>None</span>
                </label>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
