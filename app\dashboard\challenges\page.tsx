"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { Plus } from "lucide-react";
import { ChallengeCard } from "@/components/challenges/ChallengeCard";
import { ChallengeFilterBar } from "@/components/challenges/ChallengeFilterBar";
import { CHALLENGES } from "@/lib/challenges_mockup";
import { useRouter } from "next/navigation";
import { ChallengeCardSkeleton } from "@/components/challenges/ChallengeCardSkeleton";
import 'react-loading-skeleton/dist/skeleton.css';
import Link from "next/link";
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
const AREAS = [
  "All Areas",
  "Finance",
  "Love/Relationships",
  "Family",
  "Creative",
  "Physical",
  "Social",
  "Professional",
  "Emotional",
  "Spiritual",
];

export default function ChallengesPage() {
  const [selectedArea, setSelectedArea] = useState("All Areas");
  const [challenges, setChallenges] = useState<typeof CHALLENGES>([]);
  const router = useRouter();
  const userRole = useSelector((state: RootState) => state.user.user?.role);

  useEffect(() => {
    setChallenges(CHALLENGES);
  }, []);

  const filteredChallenges =
    selectedArea === "All Areas"
      ? challenges
      : challenges.filter((c: typeof CHALLENGES[number]) => c.areas.includes(selectedArea));

  return (
      <div className="w-full">
        <div className="flex items-center justify-between mb-6">
          <p className="text-display-md text-paragraphContent">My Challenges</p>
          {userRole === 'provider' && (
            <Link href="/dashboard/challenges/new">
              <Button variant="outlinedDark" size="sm">
                <Plus className="w-4 h-4" /> <span className="hidden sm:block">New Challenge</span>
              </Button>
            </Link>
          )}
        </div>
        <ChallengeFilterBar
          areas={AREAS}
          selectedArea={selectedArea}
          onSelect={setSelectedArea}
        />
        <div className="flex flex-wrap justify-around gap-6">
          {filteredChallenges.length === 0
            ? Array.from({ length: 8 }).map((_, idx) => <ChallengeCardSkeleton key={idx} />)
            : filteredChallenges.map((challenge: typeof CHALLENGES[number], idx: number) => {
              console.log(challenge);
              return (
                <ChallengeCard
                  key={idx}
                  title={challenge.title}
                  image={challenge.image}
                  areas={challenge.areas}
                  steps={challenge.steps}
                  duration={challenge.duration}
                  reward={challenge.reward}
                  isPublic={challenge.isPublic}
                  participants={challenge.participants}
                  onDetailsClick={() => router.push(`/dashboard/challenges/${challenge.id}`)}
                  onResumeClick={() => router.push(`/dashboard/challenges/new?challengeId=${challenge.id}`)}
                />
              )
            })}
        </div>
      </div>
  );
}