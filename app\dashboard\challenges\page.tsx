"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { Plus } from "lucide-react";
import { ChallengeCard } from "@/components/dashboard/ChallengeCard";
import { ChallengeFilterBar } from "@/components/dashboard/ChallengeFilterBar";
import { CHALLENGES } from "@/lib/challenges_mockup";
import { useRouter } from "next/navigation";
import { ChallengeCardSkeleton } from "@/components/dashboard/ChallengeCardSkeleton";
import 'react-loading-skeleton/dist/skeleton.css';
import Link from "next/link";
const AREAS = [
  "All Areas",
  "Finance",
  "Love/Relationships",
  "Family",
  "Creative",
  "Physical",
  "Social",
  "Professional",
  "Emotional",
  "Spiritual",
];

export default function ChallengesPage() {
  const [selectedArea, setSelectedArea] = useState("All Areas");
  const [challenges, setChallenges] = useState<typeof CHALLENGES>([]);
  const router = useRouter();

  useEffect(() => {
    setChallenges(CHALLENGES);
  }, []);

  const filteredChallenges =
    selectedArea === "All Areas"
      ? challenges
      : challenges.filter((c: typeof CHALLENGES[number]) => c.areas.includes(selectedArea));

  return (
      <div className="w-full">
        <div className="flex items-center justify-between mb-6">
          <p className="text-display-md text-paragraphContent">My Challenges</p>
          <Link href="/dashboard/challenges/new">
            <Button variant="outlinedDark" size="sm">
              <Plus className="w-4 h-4" /> <span className="hidden sm:block">New Challenge</span>
            </Button>
          </Link>
        </div>
        <ChallengeFilterBar
          areas={AREAS}
          selectedArea={selectedArea}
          onSelect={setSelectedArea}
        />
        <div className="flex flex-wrap justify-around gap-6">
          {filteredChallenges.length === 0
            ? Array.from({ length: 8 }).map((_, idx) => <ChallengeCardSkeleton key={idx} />)
            : filteredChallenges.map((challenge: typeof CHALLENGES[number], idx: number) => (
                <ChallengeCard
                  key={idx}
                  title={challenge.title}
                  image={challenge.image}
                  areas={challenge.areas}
                  steps={challenge.steps}
                  duration={challenge.duration}
                  reward={challenge.reward}
                  participants={challenge.participants}
                  onDetailsClick={() => router.push(`/dashboard/challenges/${challenge.id}`)}
                />
              ))}
        </div>
      </div>
  );
} 