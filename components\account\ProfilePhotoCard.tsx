import Image from "next/image";
import { UserAvatar } from "@/utils/icons";

interface ProfilePhotoCardProps {
  photoPreview: string;
  onPhotoChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onRemove: () => void;
}

export function ProfilePhotoCard({ photoPreview, onPhotoChange, onRemove }: ProfilePhotoCardProps) {
  return (
    <div className="bg-white border rounded p-6 max-md:px-0 mb-2">
      <div className="text-body-lg font-bold mb-4 max-md:text-center">
        Profile Photo
      </div>
      <div className="flex justify-between items-center">
        <div className="flex gap-4 sm:gap-6 md:w-auto flex-col">
          <div className="flex items-center max-md:flex-col">
            <div className="w-20 h-20 sm:w-[96px] sm:h-[96px] rounded-full bg-landingBackground flex items-center justify-center text-3xl sm:text-4xl text-gray-300 mb-2 overflow-hidden border border-gray-200">
              {photoPreview ? (
                <Image
                  src={photoPreview}
                  alt="Profile"
                  width={96}
                  height={96}
                  className="rounded-full object-cover w-full h-full"
                />
              ) : (
                <UserAvatar />
              )}
            </div>
            <div className="text-center ml-4 sm:ml-8">
              <label className="mb-1 w-full" htmlFor="photo-upload">
                <div className="flex items-center text-body-large justify-center w-full px-4 h-9 mb-1 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded flex gap-2 cursor-pointer transition">
                  Upload Photo
                </div>
                <input
                  id="photo-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={onPhotoChange}
                />
              </label>
              <button
                className="text-[#001D6C] text-body-large mt-1"
                type="button"
                onClick={onRemove}
              >
                remove
              </button>
            </div>
          </div>
        </div>
        <div className="hidden md:block w-px bg-darkBlueBackground h-24 mr-6" />
        <div className="mt-4 md:mt-0 max-md:text-center text-paragraphContent pr-6">
          <div className="text-body-lg">Image requirements:</div>
          <ul className="text-body-medium mt-2 flex flex-col gap-1 ml-2">
            <li>Min. 400 x 400px</li>
            <li>Max. 2MB</li>
            <li>Your face or company logo</li>
          </ul>
        </div>
      </div>
    </div>
  );
}