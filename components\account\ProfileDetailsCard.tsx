import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Profile } from "./types";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";

interface ProfileDetailsCardProps {
  profile: Profile;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  onSave: () => void;
  isDirty: boolean;
}

export function ProfileDetailsCard({ profile, onChange, onSave, isDirty }: ProfileDetailsCardProps) {
  return (
    <div className="bg-white border rounded p-6 text-paragraphContent">
      <div className="text-body-lg font-paragraphContent mb-4 font-bold">
        Profile Details
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <div>
          <label className="text-body-medium text-paragraphContent mb-1">
            Full Name
          </label>
          <Input
            className="w-full bg-landingBackground h-10"
            name="name"
            value={profile.name}
            onChange={onChange}
          />
          <div className="text-body-small text-coolGrey60  mt-1">
            We won't show your full name publicly.
          </div>
        </div>
        <div>
          <label className="text-body-medium text-paragraphContent mb-1">
            Username
          </label>
          <Input
            className="text-body-medium text-paragraphContent bg-landingBackground  h-10"
            name="username"
            value={profile.username}
            onChange={onChange}
          />
          <div className="text-body-small text-coolGrey60 mt-1">
            This will be public. You can change it anytime.
          </div>
        </div>
        <div>
          <label className="text-body-medium text-paragraphContent mb-1">
            Licensed State
          </label>
          <Select value={profile.state} onValueChange={value => onChange({ target: { name: "state", value } } as any)}>
            <SelectTrigger className="w-full p-2 bg-landingBackground h-10">
              <SelectValue placeholder="Select state..." />
            </SelectTrigger>
            <SelectContent className="bg-landingBackground">
              <SelectItem value="Florida">Florida</SelectItem>
              <SelectItem value="California">California</SelectItem>
              <SelectItem value="New York">New York</SelectItem>
              {/* Add more states as needed */}
            </SelectContent>
          </Select>
        </div>
        <div>
          <label className="text-body-medium text-paragraphContent mb-1">
            Affiliated Organization
          </label>
          <Input
            className="w-full bg-landingBackground h-10"
            name="org"
            value={profile.org}
            onChange={onChange}
          />
        </div>
        <div>
          <label className="text-body-medium text-paragraphContent mb-1">
            Email Address
          </label>
          <Input
            className="w-full bg-landingBackground h-10"
            name="email"
            value={profile.email}
            onChange={onChange}
          />
        </div>
        <div>
          <label className="text-body-medium text-paragraphContent mb-1">
            Phone Number
          </label>
          <Input
            className="w-full bg-landingBackground h-10"
            name="phone"
            value={profile.phone}
            onChange={onChange}
          />
        </div>
      </div>
      <div className="flex justify-end mt-6">
        <Button
          className={`w-full h-9 sm:w-auto bg-[#E6E7EA] text-netural px-8 h-10 rounded font-medium shadow-none ${
            !isDirty ? "opacity-50 cursor-not-allowed" : ""
          }`}
          type="button"
          onClick={onSave}
          disabled={!isDirty}
        >
          Save
        </Button>
      </div>
    </div>
  );
}