"use client";
import { useState, useEffect } from "react";
import {
  PatientPrevBtn,
  ProgressBtn,
  ProgressCheck,
} from "@/utils/icons";
import dayjs from "dayjs";
import PatientDetailsForm from "@/components/patients/PatientDetailsFrom";
import PatientPlanForm from "@/components/patients/PatientPlanForm";
import PatientInviteForm from "@/components/patients/PatientInviteForm";

const initialForm = {
  name: "",
  dob: "",
  military: "",
  plan: "",
  card: "",
  email: "",
  phone: "",
};

const cardDetails: Record<
  string,
  { number: string; expiry: string; cvv: string }
> = {
  card1: {
    number: "4889 9271 1937 1932",
    expiry: "12/28",
    cvv: "***",
  },
  card2: {
    number: "5234 5678 9012 3456",
    expiry: "09/27",
    cvv: "***",
  },
};

export default function NewPatientPage() {
  const [step, setStep] = useState(1);
  const [form, setForm] = useState(initialForm);
  const [errors, setErrors] = useState<any>({});
  const [dobValue, setDobValue] = useState<dayjs.Dayjs | null>(null);

  // Add useEffect to handle initial value
  useEffect(() => {
    if (form.dob) {
      setDobValue(dayjs(form.dob));
    }
  }, [form.dob]);

  // Validation functions
  const validateStep1 = () => {
    const errs: any = {};
    if (!form.name.trim()) errs.name = "Patient name is required";
    if (!form.dob) errs.dob = "Date of birth is required";
    if (!form.military) errs.military = "Please select an option";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };
  const validateStep2 = () => {
    const errs: any = {};
    if (!form.plan) errs.plan = "Please select a plan";
    if (!form.card) errs.card = "Please select a card";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };
  const validateStep3 = () => {
    const errs: any = {};
    if (!form.email && !form.phone) errs.email = "Email or phone is required";
    if (form.email && !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email))
      errs.email = "Invalid email";
    if (form.phone && !/^\+?\d{7,}$/.test(form.phone))
      errs.phone = "Invalid phone number";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };

  const selectedCard = cardDetails[form.card] || cardDetails["card1"];

  return (
    <div>
      {/* Header */}
      <div className="mb-4 sm:mb-8">
        <p className="text-display-md text-paragraphContent">New Patient</p>
        <a
          href="/dashboard/patients"
          className="flex items-center mt-6 sm:mt-11 pb-3 mb-4 sm:mb-6 border-b border-gray-200"
        >
          <PatientPrevBtn />
          <span className="ml-2 text-title-medium text-contentColor">
            Back to All Patients
          </span>
        </a>
      </div>
      <div className="w-[748px] bg-white shadow mt-4">
        {/* Progress Bar */}
        <div className="flex flex-col sm:flex-row items-center">
          {/* Step 1 */}
          <div
            className={`flex flex-col items-center w-full sm:flex-1 py-4 border-t-[4px]  ${
              step > 1 ? "bg-landingBackground border-darkerBlue" : "border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              {step > 1 ? (                
                <ProgressCheck />
              ) : (
                <span className="mr-2">
                  <ProgressBtn />
                </span>
              )}
              <span
                className={`text-body-medium text-contentColor ${
                  step === 1
                    ? ""
                    : step > 1
                    ? ""
                    : "text-coolGrey400"
                }`}
              >
                1. Fill out Patient Details
              </span>
            </div>
          </div>
          {/* Step 2 */}
          <div
            className={`flex flex-col items-center w-full sm:flex-1 py-4  border-t-[4px] bg-landingBackground ${
              step > 2
                ? "border-darkerBlue"
                : step > 2
                ? "bg-landingBackground border-mediumGray"
                : "bg-transparent border-mediumGray"
            }`}
          >
            <div className="flex items-center  h-5">
              {step > 2 ? (
                <ProgressCheck />
              ) : step == 2 ? (
                <span className="mr-2">
                  <ProgressBtn />
                </span>
              ) : (
                <span
                  className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                    step === 2
                      ? "border-2 border-blue-900"
                      : "border text-coolGrey400"
                  }`}
                ></span>
              )}
              <span
                className={`text-body-medium text-contentColor ${
                  step === 2
                    ? ""
                    : step > 2
                    ? ""
                    : "text-coolGrey400"
                }`}
              >
                2. Purchase a Patient Plan
              </span>
            </div>
          </div>
          {/* Step 3 */}
          <div
            className={`flex flex-col items-center w-full sm:flex-1 py-4 border-t-[4px] bg-landingBackground ${
              step > 3
                ? "border-darkerBlue"
                : "bg-transparent border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              <span
                className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                  step === 3
                    ? "border-2 border-blue-900"
                    : "border border-gray-400"
                }`}
              ></span>
              <span
                className={`text-body-medium text-contentColor ${
                  step === 3 ? "" : "text-coolGrey400"
                }`}
              >
                3. Invite your Patient
              </span>
            </div>
          </div>
        </div>
        <div className="max-w-[748px] mx-auto">
          {/* Step 1: Patient Details */}
          {step === 1 && (
            <PatientDetailsForm
              form={form}
              setForm={setForm}
              errors={errors}
              dobValue={dobValue}
              setDobValue={setDobValue}
              onNext={() => { if (validateStep1()) setStep(2); }}
            />
          )}

          {step === 2 && (
            <PatientPlanForm
              form={form}
              setForm={setForm}
              errors={errors}
              onBack={() => setStep(1)}
              onNext={() => { if (validateStep2()) setStep(3); }}
            />
          )}

          {/* Step 3: Invite Patient */}
          {step === 3 && (
            <PatientInviteForm
              form={form}
              setForm={setForm}
              errors={errors}
              onSubmit={() => validateStep3()}
            />
          )}
        </div>
      </div>
    </div>
  );
}
