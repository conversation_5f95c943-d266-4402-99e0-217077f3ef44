"use client";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  PatientPrevBtn,
  ViewEyeBtn,
  CopyCard,
  ProgressBtn,
  ProgressCheck,
} from "@/utils/icons";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import DatePicker from "@/components/ui/DatePicker";
import dayjs from "dayjs";

const initialForm = {
  name: "",
  dob: "",
  military: "",
  plan: "",
  card: "",
  email: "",
  phone: "",
};

const cardDetails: Record<
  string,
  { number: string; expiry: string; cvv: string }
> = {
  card1: {
    number: "4889 9271 1937 1932",
    expiry: "12/28",
    cvv: "***",
  },
  card2: {
    number: "5234 5678 9012 3456",
    expiry: "09/27",
    cvv: "***",
  },
};

export default function NewPatientPage() {
  const [step, setStep] = useState(1);
  const [form, setForm] = useState(initialForm);
  const [errors, setErrors] = useState<any>({});
  const [dobValue, setDobValue] = useState<dayjs.Dayjs | null>(null);

  // Add useEffect to handle initial value
  useEffect(() => {
    if (form.dob) {
      setDobValue(dayjs(form.dob));
    }
  }, [form.dob]);

  // Validation functions
  const validateStep1 = () => {
    const errs: any = {};
    if (!form.name.trim()) errs.name = "Patient name is required";
    if (!form.dob) errs.dob = "Date of birth is required";
    if (!form.military) errs.military = "Please select an option";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };
  const validateStep2 = () => {
    const errs: any = {};
    if (!form.plan) errs.plan = "Please select a plan";
    if (!form.card) errs.card = "Please select a card";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };
  const validateStep3 = () => {
    const errs: any = {};
    if (!form.email && !form.phone) errs.email = "Email or phone is required";
    if (form.email && !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email))
      errs.email = "Invalid email";
    if (form.phone && !/^\+?\d{7,}$/.test(form.phone))
      errs.phone = "Invalid phone number";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };

  const selectedCard = cardDetails[form.card] || cardDetails["card1"];

  return (
    <div>
      {/* Header */}
      <div className="mb-4 sm:mb-8">
        <p className="text-display-md text-paragraphContent">New Patient</p>
        <a
          href="/dashboard/patients"
          className="flex items-center mt-6 sm:mt-11 pb-3 mb-4 sm:mb-6 border-b border-gray-200"
        >
          <PatientPrevBtn />
          <span className="ml-2 text-title-medium text-contentColor">
            Back to All Patients
          </span>
        </a>
      </div>
      <div className="w-[748px] bg-white shadow mt-4">
        {/* Progress Bar */}
        <div className="flex flex-col sm:flex-row items-center">
          {/* Step 1 */}
          <div
            className={`flex flex-col items-center w-full sm:flex-1 py-4 border-t-[4px] bg-landingBackground  ${
              step > 1 ? "border-darkerBlue" : "border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              {step > 1 ? (                
                <ProgressCheck />
              ) : (
                <span className="mr-2">
                  <ProgressBtn />
                </span>
              )}
              <span
                className={`text-title-small text-contentColor ${
                  step === 1
                    ? "font-bold"
                    : step > 1
                    ? "font-bold"
                    : "text-coolGrey400"
                }`}
              >
                1. Fill out Patient Details
              </span>
            </div>
          </div>
          {/* Step 2 */}
          <div
            className={`flex flex-col items-center w-full sm:flex-1 py-4  border-t-[4px] bg-landingBackground ${
              step > 2
                ? "border-darkerBlue"
                : step > 2
                ? "bg-landingBackground border-mediumGray"
                : "bg-transparent border-mediumGray"
            }`}
          >
            <div className="flex items-center  h-5">
              {step > 2 ? (
                <ProgressCheck />
              ) : step == 2 ? (
                <span className="mr-2">
                  <ProgressBtn />
                </span>
              ) : (
                <span
                  className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                    step === 2
                      ? "border-2 border-blue-900"
                      : "border text-coolGrey400"
                  }`}
                ></span>
              )}
              <span
                className={`text-title-small text-contentColor ${
                  step === 2
                    ? "font-bold"
                    : step > 2
                    ? "font-bold"
                    : "text-coolGrey400"
                }`}
              >
                2. Purchase a Patient Plan
              </span>
            </div>
          </div>
          {/* Step 3 */}
          <div
            className={`flex flex-col items-center w-full sm:flex-1 py-4 border-t-[4px] bg-landingBackground ${
              step > 3
                ? "border-darkerBlue"
                : "bg-transparent border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              <span
                className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                  step === 3
                    ? "border-2 border-blue-900"
                    : "border border-gray-400"
                }`}
              ></span>
              <span
                className={`text-title-small text-contentColor ${
                  step === 3 ? "font-bold" : "text-coolGrey400"
                }`}
              >
                3. Invite your Patient
              </span>
            </div>
          </div>
        </div>
        <div className="max-w-[748px] mx-auto p-16">
          {/* Step 1: Patient Details */}
          {step === 1 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (validateStep1()) setStep(2);
              }}
            >
              <div className="mb-4">
                <p className="text-paragraphContent text-body-lg font-bold mb-3">
                  Fill out patient details:
                </p>
                <hr className="mb-3 border-border" />
              </div>
              <div className="mb-8">
                <label className="block mb-1 text-body-medium text-paragraphContent font-bold">
                  Patient Name
                </label>
                <Input
                  className={`w-full mb-1 bg-landingBackground${
                    errors.name ? " ring-2 ring-red-400" : ""
                  }`}
                  name="name"
                  value={form.name}
                  onChange={(e) =>
                    setForm((f) => ({ ...f, name: e.target.value }))
                  }
                  required
                  placeholder=""
                />
                {errors.name && (
                  <div className="text-red-500 text-sm mb-2">{errors.name}</div>
                )}
                <div className="text-body-small text-paragraphContent mb-3">
                  We won't show your full name publicly.
                </div>
              </div>
              <div className="mb-8">
                <label className="block mb-1 text-body-medium text-paragraphContent font-bold">
                  Patient Date of Birth
                </label>
                <DatePicker
                  value={dobValue}
                  onChange={(newValue) => {
                    setDobValue(newValue);
                    setForm((f) => ({ ...f, dob: newValue ? newValue.format("YYYY-MM-DD") : "" }));
                  }}
                  error={!!errors.dob}
                  helperText={errors.dob}
                  required
                />
              </div>
              <div className="mb-8">
                <label className="block mb-1 text-body-medium text-paragraphContent font-bold">
                  Is the patient a military vet?
                </label>
                <div className="relative mb-4 sm:mb-6">
                  <select
                    className={`w-full p-2 sm:p-3 border-none rounded bg-landingBackground appearance-none${
                      errors.military ? " ring-2 ring-red-400" : ""
                    }`}
                    name="military"
                    value={form.military}
                    onChange={(e) =>
                      setForm((f) => ({ ...f, military: e.target.value }))
                    }
                    required
                  >
                    <option value="">Select a option</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
                    <svg
                      width="20"
                      height="20"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                    >
                      <path d="M6 9l6 6 6-6" />
                    </svg>
                  </span>
                </div>
                {errors.military && (
                  <div className="text-red-500 text-sm mb-2">
                    {errors.military}
                  </div>
                )}
              </div>
              <div className="flex justify-end">
                <Button
                  type="submit"
                  size="sm"
                  className="bg-[#556080] hover:bg-[#3d4250] text-white px-6 sm:px-8 py-2 rounded shadow-none font-medium w-full sm:w-auto"
                >
                  Next
                </Button>
              </div>
            </form>
          )}

          {/* Step 2: Purchase Plan */}
          {step === 2 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (validateStep2()) setStep(3);
              }}
              className="max-w-[431px] mx-auto"
            >
              <h2 className="text-lg sm:text-xl font-semibold mb-2">
                Select a patient plan:
              </h2>
              <hr className="mb-4 sm:mb-6 border-gray-200" />
              <div className="flex flex-col sm:flex-row gap-4 mb-4 sm:mb-6">
                <button
                  type="button"
                  className={`flex-1 border rounded-lg p-3 sm:p-4 bg-white shadow-sm flex flex-col items-start transition-all duration-150 ${
                    form.plan === "annual"
                      ? "border-green-600"
                      : "border-gray-200"
                  }`}
                  onClick={() => setForm((f) => ({ ...f, plan: "annual" }))}
                >
                  <span className="text-xs text-gray-500 mb-1">
                    Annual Subscription
                  </span>
                  <div className="flex items-end gap-2">
                    <span className="font-bold text-xl sm:text-2xl text-gray-900">
                      $116
                    </span>
                    <span className="text-sm text-gray-500">/yr</span>
                    <span className="ml-2 px-2 py-0.5 text-xs rounded bg-green-100 text-green-700 font-semibold">
                      SAVE 20%
                    </span>
                  </div>
                </button>
                <button
                  type="button"
                  className={`flex-1 border rounded-lg p-3 sm:p-4 bg-white shadow-sm flex flex-col items-start transition-all duration-150 ${
                    form.plan === "monthly"
                      ? "border-green-600"
                      : "border-gray-200"
                  }`}
                  onClick={() => setForm((f) => ({ ...f, plan: "monthly" }))}
                >
                  <span className="text-xs text-gray-500 mb-1">
                    Monthly Subscription
                  </span>
                  <div className="flex items-end gap-2">
                    <span className="font-bold text-xl sm:text-2xl text-gray-900">
                      $12
                    </span>
                    <span className="text-sm text-gray-500">/mo</span>
                  </div>
                </button>
              </div>
              {errors.plan && (
                <div className="text-red-500 text-sm mb-2">{errors.plan}</div>
              )}
              <label className="block mb-2 font-medium">
                Select a payment method:
              </label>
              <div className="mb-4 sm:mb-6">
                <select
                  className={`w-full p-2 sm:p-3 border rounded-lg bg-white ${
                    errors.card ? "border-red-500" : "border-gray-200"
                  }`}
                  value={form.card}
                  onChange={(e) =>
                    setForm((f) => ({ ...f, card: e.target.value }))
                  }
                  required
                >
                  <option value="">Primary Card</option>
                  <option value="card1">Card 1</option>
                  <option value="card2">Card 2</option>
                </select>
                {errors.card && (
                  <div className="text-red-500 text-sm mb-2">{errors.card}</div>
                )}
              </div>
              <div className="mb-6 sm:mb-8">
                <div className="p-3 sm:p-4 border rounded-lg bg-white shadow-sm">
                  <div className="mb-4">
                    <Image
                      src="/dashboard/card.png"
                      alt="Card"
                      width={638}
                      height={112}
                      priority
                      className="rounded-xl object-cover w-full"
                    />
                  </div>
                  <div className="px-2 sm:px-4">
                    <div className="font-semibold mb-3 text-sm sm:text-[15px]">
                      Card Information
                    </div>
                    <div className="grid grid-cols-2 gap-y-2 gap-x-4 sm:gap-x-6 text-sm sm:text-[15px]">
                      {/* Card No. */}
                      <div className="flex items-center gap-2">
                        <span className="text-gray-600">Card No.</span>
                      </div>
                      <div className="flex items-center gap-2 justify-end">
                        {/* Copy icon */}
                        <button
                          type="button"
                          className="text-gray-400 hover:text-gray-700"
                          tabIndex={-1}
                          aria-label="Copy card number"
                        >
                          <CopyCard />
                        </button>
                        <span className="tracking-wider text-gray-900">
                          {selectedCard.number}
                        </span>
                      </div>
                      {/* Expiry date */}
                      <div className="flex items-center gap-2">
                        <span className="text-gray-600">Expiry date</span>
                      </div>
                      <div className="flex items-center gap-2 justify-end">
                        <span className="text-gray-900">
                          {selectedCard.expiry}
                        </span>
                      </div>
                      {/* CVV */}
                      <div className="flex items-center gap-2">
                        <span className="text-gray-600">
                          CVV (3-digit security code)
                        </span>
                      </div>
                      <div className="flex items-center gap-2 justify-end">
                        {/* Eye-off icon */}
                        <span className="text-gray-400">
                          <ViewEyeBtn />
                        </span>
                        <span className="tracking-widest text-gray-900">
                          {selectedCard.cvv}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row justify-between gap-4">
                <Button
                  variant="netural"
                  size="sm"
                  className="text-lightBlue"
                  onClick={() => setStep(1)}
                >
                  Back
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  className="text-lightBlue"
                >
                  Submit Purchase
                </Button>
              </div>
            </form>
          )}

          {/* Step 3: Invite Patient */}
          {step === 3 && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                if (validateStep3()) {
                  // handle invite logic here
                  alert("Patient invited!");
                }
              }}
              className="max-w-[500px] p-6 mx-auto"
            >
              <p className="text-body-lg font-bold text-netural90 mb-2">
                Your patient plan was successfully purchased!
              </p>
              <div className="text-body-medium text-netural90 mb-2">
                We sent you a confirmation email, and you can now see your new
                patient in{" "}
                <a
                  href="/dashboard/patients"
                  className="text-blue-700 underline font-medium"
                >
                  My Patients
                </a>
                .
              </div>
              <div className="text-body-lg font-bold text-netural90 mb-4">
                Now just invite your patient to their account:
              </div>
              <label className="text-body-medium text-netural90 font-bold">Patient Email</label>
              <Input
                className={`w-full mb-2 p-2 mt-2 sm:p-3 rounded bg-landingBackground${
                  errors.email ? " ring-2 ring-red-400" : ""
                }`}
                name="email"
                value={form.email}
                onChange={(e) =>
                  setForm((f) => ({ ...f, email: e.target.value }))
                }
                placeholder=""
                autoComplete="off"
              />
              {errors.email && (
                <div className="text-red-500 text-sm mb-2">{errors.email}</div>
              )}
              <div className="text-center my-4 text-netural90 font-bold">
                OR
              </div>
              <label className="text-body-medium text-netural90 font-bold">Patient Phone</label>
              <Input
                className={`w-full mb-4 p-2 mt-2 sm:p-3 rounded bg-landingBackground${
                  errors.phone ? " ring-2 ring-red-400" : ""
                }`}
                name="phone"
                value={form.phone}
                onChange={(e) =>
                  setForm((f) => ({ ...f, phone: e.target.value }))
                }
                placeholder=""
                autoComplete="off"
              />
              {errors.phone && (
                <div className="text-red-500 text-sm mb-2">{errors.phone}</div>
              )}
              <div className="flex justify-end mt-12">
                <Button
                  variant="netural"
                  size="sm"
                  className="text-lightBlue"
                >
                  Send Invite
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
