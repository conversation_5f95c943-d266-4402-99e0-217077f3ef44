import type { Metadata } from 'next';
import './globals.css';
import ClientHeader from './layout/client-header-wrapper';
import ClientFooter from './layout/client-footer-wrapper';
import { Providers } from './providers';

export const metadata: Metadata = {
  title: 'peakality',
  description: 'Created peakality',
  generator: 'peakality',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className="mx-auto">
        <Providers>
          <ClientHeader />
          {children}
          <ClientFooter />
        </Providers>
      </body>
    </html>
  )
}
