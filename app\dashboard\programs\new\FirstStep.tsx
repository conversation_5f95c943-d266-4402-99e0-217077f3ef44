import React from "react";
import { PreviewNewProgram } from "@/utils/icons";
import { ArrowUpFromLine } from "lucide-react";

export default function FirstStep({
  programName,
  setProgramName,
  programDescription,
  setProgramDescription,
  file,
  error,
  renderPreview,
  handleFileChange,
  handleRemove,
  setStep
}: {
  programName: string;
  setProgramName: (v: string) => void;
  programDescription: string;
  setProgramDescription: (v: string) => void;
  file: File | null;
  error: string;
  renderPreview: () => React.ReactNode;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleRemove: () => void;
  setStep: (n: number) => void;
}) {
  return (
    <div className="mt-5 px-2 sm:px-4 md:px-8 pb-8 pt-2 text-left mx-auto w-full max-w-full sm:max-w-screen-sm">
      <div className="text-lg sm:text-xl font-bold text-gray-900 mb-4">Program Details</div>
      <form onSubmit={e => { e.preventDefault(); setStep(2); }} className="space-y-4">
        <div>
          <label className="block text-sm sm:text-base font-semibold mb-1">Program Name</label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm sm:text-base"
            value={programName}
            onChange={e => setProgramName(e.target.value)}
            required
          />
        </div>
        <div>
          <label className="block text-sm sm:text-base font-semibold mb-1">Program Description</label>
          <textarea
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm sm:text-base"
            rows={4}
            value={programDescription}
            onChange={e => setProgramDescription(e.target.value)}
            required
          />
        </div>
        <div className="w-full flex flex-col items-center">
          <div className="max-w-[88px] w-full mt-5 mb-2">
            {renderPreview()}
          </div>
          {error && <div className="text-xs text-red-500 mb-2">{error}</div>}
        </div>
        <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-0 mt-8">
          <button type="button" className="w-full sm:w-auto bg-[#6b7a8f] hover:bg-[#111827] text-white px-8 py-2 rounded font-semibold shadow-sm text-base">Back</button>
          <button type="submit" className="w-full sm:w-auto bg-[#6b7a8f] hover:bg-[#4b5563] text-white px-8 py-2 rounded font-semibold shadow-sm text-base">Next</button>
        </div>
      </form>
    </div>
  );
} 