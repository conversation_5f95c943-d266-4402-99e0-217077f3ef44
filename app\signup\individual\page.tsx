"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, Calendar, ChevronDown } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useDispatch } from 'react-redux';
import { setUser } from '@/store/userSlice';

export default function IndividualSignupPage() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    fullName: "",
    username: "",
    dateOfBirth: "",
    privacyCategory: "",
    email: "",
    phone: "",
    password: "",
    rememberMe: false,
    signUpForUpdates: true
  });
  const dispatch = useDispatch();

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // In a real application, this would call an API to create the account
      console.log("Signup form data:", formData);

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Dispatch user data and role to Redux
      dispatch(setUser({ role: 'patient', username: formData.username, email: formData.email }));

      // Redirect to verify account page after successful signup
      router.push("/dashboard");
    } catch (error) {
      console.error("Signup error:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value
    });
  };

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid lg:h-[calc(100vh-80px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 relative">
          <Image
            src="/auth/individual.svg"
            alt="Woman with headphones writing notes"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Individual Sign Up Form */}
        <div className="w-full md:w-1/2 flex items-center justify-center">
          <div className="w-full max-w-[670px] px-8 py-12">
            <div>
              <h1 className="text-display-md text-paragraphContent mb-1 max-lg:text-3xl max-md:text-center">Create an Individual Account</h1>
              <p className="text-body-lg mb-6 text-paragraphContent max-md:text-center">
                Rhoncus morbi et augue nec, in id ullamcorper et sit.
              </p>

              <form onSubmit={onSubmit} className="space-y-4 mt-14">
                {/* Full Name and Username */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="fullName" className="text-body-medium text-paragraphContent">Full Name</label>
                    <Input
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md"
                      disabled={isSubmitting}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">We won't show your full name publicly</p>
                  </div>

                  <div>
                    <label htmlFor="username" className="text-body-medium text-paragraphContent">Username</label>
                    <div className="flex items-center bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md">
                      <span className="text-gray-500 pl-3">@</span>
                      <input
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleChange}
                        className="w-full p-3 bg-landingBackground rounded-md"
                        disabled={isSubmitting}
                        required
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">This will be public. You can change it anytime.</p>
                  </div>
                </div>

                {/* Date of Birth and Privacy Category */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="dateOfBirth" className="text-body-medium text-paragraphContent">Date of Birth</label>
                    <div className="relative w-full">
                      <Input
                        id="dateOfBirthInput"
                        name="dateOfBirth"
                        type="text"
                        value={formData.dateOfBirth}
                        onChange={handleChange}
                        className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-lg pr-10 date-input"
                        placeholder="mm/dd/yyyy"
                        disabled={isSubmitting}
                        required
                      />
                      <div
                        className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                        onClick={() => {
                          const dateInput = document.getElementById('datePickerHidden') as HTMLInputElement;
                          if (dateInput && typeof dateInput.showPicker === 'function') {
                            dateInput.showPicker();
                          }
                        }}
                      >
                        <div className="flex items-center justify-center w-6 h-6 rounded-full">
                          <Calendar className="h-5 w-5 text-gray-700" />
                        </div>
                      </div>
                      <Input
                        id="datePickerHidden"
                        type="date"
                        className="absolute opacity-0 w-0 h-0"
                        onChange={(e) => {
                          if (e.target.value) {
                            // Format the date as mm/dd/yyyy
                            const date = new Date(e.target.value);
                            const month = (date.getMonth() + 1).toString().padStart(2, '0');
                            const day = date.getDate().toString().padStart(2, '0');
                            const year = date.getFullYear();
                            const formattedDate = `${month}/${day}/${year}`;

                            // Update the visible input
                            const visibleInput = document.getElementById('dateOfBirthInput') as HTMLInputElement;
                            if (visibleInput) {
                              visibleInput.value = formattedDate;
                            }

                            // Update the form data
                            setFormData({
                              ...formData,
                              dateOfBirth: formattedDate
                            });
                          }
                        }}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="privacyCategory" className="text-body-medium text-paragraphContent">Are you a Military veteran?</label>
                    <div className="relative text-gray-600">
                      <select
                        id="privacyCategory"
                        name="privacyCategory"
                        value={formData.privacyCategory}
                        onChange={handleChange}
                        className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md appearance-none"
                        disabled={isSubmitting}
                        required
                      >
                        <option value="">Select an option</option>
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <ChevronDown className="h-5 w-5" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Email and Phone */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="email" className="text-body-medium text-paragraphContent">Email Address</label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md"
                      disabled={isSubmitting}
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="text-body-medium text-paragraphContent">Phone Number</label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md"
                      disabled={isSubmitting}
                      required
                    />
                  </div>
                </div>

                {/* Password */}
                <div>
                  <label htmlFor="password" className="text-body-medium text-paragraphContent">Create a Password</label>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={handleChange}
                      className="w-full p-3 bg-landingBackground border-0 border-b-2 border-medium-gray rounded-md pr-10"
                      disabled={isSubmitting}
                      required
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-paragraphContent" />
                      ) : (
                        <Eye className="h-5 w-5 text-paragraphContent" />
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Your password must be a combination of numbers, letters, symbols, and capitals.</p>
                </div>

                {/* Checkboxes */}
                <div className="space-y-3 mt-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rememberMe"
                      name="rememberMe"
                      checked={formData.rememberMe}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, rememberMe: checked === true })
                      }
                      disabled={isSubmitting}
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="rememberMe"
                      className="text-paragraphContent text-body-medium"
                    >
                      Remember me on this device
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="signUpForUpdates"
                      name="signUpForUpdates"
                      checked={formData.signUpForUpdates}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, signUpForUpdates: checked === true })
                      }
                      disabled={isSubmitting}
                      className="border-paragraphContent"
                    />
                    <label
                      htmlFor="signUpForUpdates"
                      className="text-paragraphContent text-body-medium"
                    >
                      Sign me up for important news and updates
                    </label>
                  </div>
                </div>

                {/* Sign Up Button */}
                <Button
                  type="submit"
                  variant="netural"
                  className="w-full font-normal h-11 rounded-sm"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Signing up..." : "Sign Up"}
                </Button>
              </form>

              {/* Login link */}
              <div className="text-sm mt-8 pt-8 border-t border-gray-200">
                Already have an account? <Link href="/login" className="font-medium underline">Log In</Link>
              </div>

              {/* Provider link */}
              <div className="text-sm mt-4">
                Are you a healthcare provider? <Link href="/signup/provider" className="font-medium underline">Create a Provider Account</Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
