"use client";

export interface CardIconProps {
  width?: number;
  height?: number;
  color?: string;
}

export const Provider = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="52"
      height="53"
      viewBox="0 0 52 53"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M49.9043 18.7324L40.7734 46.834H11.2266L2.09473 18.7324L26 1.36426L49.9043 18.7324Z"
        stroke="#546881"
        strokeWidth="1.4"
      />
      <path
        d="M24.5 28H22V25H24.5V22.5H27.5V25H30V28H27.5V30.5H24.5V28ZM26 17L18 20V26.09C18 31.14 21.41 35.85 26 37C30.59 35.85 34 31.14 34 26.09V20L26 17ZM32 26.09C32 30.09 29.45 33.79 26 34.92C22.55 33.79 20 30.1 20 26.09V21.39L26 19.14L32 21.39V26.09Z"
        fill="#546881"
      />
    </svg>
  );
};

export const Individual = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      version="1.0"
      xmlns="http://www.w3.org/2000/svg"
      width="55"
      height="55"
      viewBox="0 0 65.000000 67.000000"
      preserveAspectRatio="xMidYMid meet"
    >
      <g
        transform="translate(0.000000,67.000000) scale(0.100000,-0.100000)"
        fill="#546881"
        stroke="none"
      >
        <path
          d="M201 495 c-79 -57 -148 -108 -153 -113 -7 -7 10 -71 48 -186 l59
                        -176 190 0 190 0 55 173 c30 94 55 175 55 179 0 6 -57 49 -250 190 l-51 36
                        -143 -103z m184 -135 c41 -45 -18 -110 -70 -76 -26 17 -32 45 -15 77 14 25 62
                        25 85 -1z m33 -116 c14 -10 22 -25 22 -45 0 -36 -22 -35 -30 2 -5 22 -12 24
                        -68 27 -62 3 -62 3 -68 -28 -8 -37 -24 -40 -24 -3 0 45 26 63 89 63 35 0 65
                        -6 79 -16z"
        />
        <path
          d="M324 345 c-9 -23 4 -47 23 -43 22 4 30 38 11 49 -19 13 -28 11 -34
                        -6z"
        />
      </g>
    </svg>
  );
};

export const UserIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="16"
      height="21"
      viewBox="0 0 16 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 5.46021C12 7.66934 10.2091 9.46021 8 9.46021C5.79086 9.46021 4 7.66934 4 5.46021C4 3.25107 5.79086 1.46021 8 1.46021C10.2091 1.46021 12 3.25107 12 5.46021Z"
        stroke="#1D1B20"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 12.4602C4.13401 12.4602 1 15.5942 1 19.4602H15C15 15.5942 11.866 12.4602 8 12.4602Z"
        stroke="#1D1B20"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ChartPie = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 3.51514C6.50005 4.01258 3 7.82765 3 12.4602C3 17.4308 7.02944 21.4602 12 21.4602C16.6326 21.4602 20.4476 17.9602 20.9451 13.4602H11V3.51514Z"
        stroke="#21272A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.4878 9.46021H15V3.97251C17.5572 4.87634 19.5839 6.90305 20.4878 9.46021Z"
        stroke="#21272A"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Program = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="19"
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 11.4602L19 6.46021L10 1.46021L1 6.46021L10 11.4602ZM10 11.4602L16.1591 8.03846C16.7017 9.40679 17 10.8986 17 12.4601C17 13.1615 16.9398 13.8487 16.8244 14.5171C14.2143 14.7708 11.849 15.8608 10 17.5157C8.15096 15.8608 5.78571 14.7709 3.17562 14.5171C3.06017 13.8487 3 13.1614 3 12.4601C3 10.8986 3.29824 9.40677 3.84088 8.03845L10 11.4602ZM6 17.4602V9.96021L10 7.73798"
        stroke="#1D1B20"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Ebook = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="17"
      viewBox="0 0 20 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 2.71299V15.713M10 2.71299C8.83211 1.93707 7.24649 1.46021 5.5 1.46021C3.75351 1.46021 2.16789 1.93707 1 2.71299V15.713C2.16789 14.9371 3.75351 14.4602 5.5 14.4602C7.24649 14.4602 8.83211 14.9371 10 15.713M10 2.71299C11.1679 1.93707 12.7535 1.46021 14.5 1.46021C16.2465 1.46021 17.8321 1.93707 19 2.71299V15.713C17.8321 14.9371 16.2465 14.4602 14.5 14.4602C12.7535 14.4602 11.1679 14.9371 10 15.713"
        stroke="#1D1B20"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Challenge = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="22"
      height="19"
      viewBox="0 0 22 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16 17.4602H21V15.4602C21 13.8034 19.6569 12.4602 18 12.4602C17.0444 12.4602 16.1931 12.907 15.6438 13.6031M16 17.4602H6M16 17.4602V15.4602C16 14.804 15.8736 14.1773 15.6438 13.6031M6 17.4602H1V15.4602C1 13.8034 2.34315 12.4602 4 12.4602C4.95561 12.4602 5.80686 12.907 6.35625 13.6031M6 17.4602V15.4602C6 14.804 6.12642 14.1773 6.35625 13.6031M6.35625 13.6031C7.0935 11.7612 8.89482 10.4602 11 10.4602C13.1052 10.4602 14.9065 11.7612 15.6438 13.6031M14 4.46021C14 6.11706 12.6569 7.46021 11 7.46021C9.34315 7.46021 8 6.11706 8 4.46021C8 2.80335 9.34315 1.46021 11 1.46021C12.6569 1.46021 14 2.80335 14 4.46021ZM20 7.46021C20 8.56477 19.1046 9.46021 18 9.46021C16.8954 9.46021 16 8.56477 16 7.46021C16 6.35564 16.8954 5.46021 18 5.46021C19.1046 5.46021 20 6.35564 20 7.46021ZM6 7.46021C6 8.56477 5.10457 9.46021 4 9.46021C2.89543 9.46021 2 8.56477 2 7.46021C2 6.35564 2.89543 5.46021 4 5.46021C5.10457 5.46021 6 6.35564 6 7.46021Z"
        stroke="#1D1B20"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Account = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.12104 16.2639C5.15267 15.1156 7.4998 14.4602 10 14.4602C12.5002 14.4602 14.8473 15.1156 16.879 16.2639M13 8.46021C13 10.1171 11.6569 11.4602 10 11.4602C8.34315 11.4602 7 10.1171 7 8.46021C7 6.80335 8.34315 5.46021 10 5.46021C11.6569 5.46021 13 6.80335 13 8.46021ZM19 10.4602C19 15.4308 14.9706 19.4602 10 19.4602C5.02944 19.4602 1 15.4308 1 10.4602C1 5.48964 5.02944 1.46021 10 1.46021C14.9706 1.46021 19 5.48964 19 10.4602Z"
        stroke="#1D1B20"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const GreenCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.73298 3.9991C4.1396 3.96645 6.33232 3.04824 8.0001 1.55554C9.66787 3.04824 11.8606 3.96645 14.2672 3.9991C14.3546 4.51972 14.4001 5.05455 14.4001 5.6C14.4001 9.77988 11.7287 13.3358 8.0001 14.6537C4.27148 13.3358 1.6001 9.77988 1.6001 5.6C1.6001 5.05455 1.64559 4.51972 1.73298 3.9991ZM10.9658 6.96566C11.2782 6.65324 11.2782 6.14671 10.9658 5.83429C10.6534 5.52187 10.1468 5.52187 9.83441 5.83429L7.2001 8.4686L6.16578 7.43429C5.85336 7.12187 5.34683 7.12187 5.03441 7.43429C4.72199 7.74671 4.72199 8.25324 5.03441 8.56566L6.63441 10.1657C6.94683 10.4781 7.45336 10.4781 7.76578 10.1657L10.9658 6.96566Z"
        fill="#8BBB4B"
      />
    </svg>
  );
};

export const RedCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.732984 2.9991C3.1396 2.96645 5.33232 2.04824 7.0001 0.555542C8.66787 2.04824 10.8606 2.96645 13.2672 2.9991C13.3546 3.51972 13.4001 4.05455 13.4001 4.6C13.4001 8.77988 10.7287 12.3358 7.0001 13.6537C3.27148 12.3358 0.600098 8.77988 0.600098 4.6C0.600098 4.05455 0.645589 3.51972 0.732984 2.9991ZM9.96578 5.96566C10.2782 5.65324 10.2782 5.14671 9.96578 4.83429C9.65336 4.52187 9.14683 4.52187 8.83441 4.83429L6.2001 7.4686L5.16578 6.43429C4.85336 6.12187 4.34683 6.12187 4.03441 6.43429C3.72199 6.74671 3.72199 7.25324 4.03441 7.56566L5.63441 9.16566C5.94683 9.47808 6.45336 9.47808 6.76578 9.16566L9.96578 5.96566Z"
        fill="#CC1F70"
      />
    </svg>
  );
};

export const LightRedCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.732984 2.9991C3.1396 2.96645 5.33232 2.04824 7.0001 0.555542C8.66787 2.04824 10.8606 2.96645 13.2672 2.9991C13.3546 3.51972 13.4001 4.05455 13.4001 4.6C13.4001 8.77988 10.7287 12.3358 7.0001 13.6537C3.27148 12.3358 0.600098 8.77988 0.600098 4.6C0.600098 4.05455 0.645589 3.51972 0.732984 2.9991ZM9.96578 5.96566C10.2782 5.65324 10.2782 5.14671 9.96578 4.83429C9.65336 4.52187 9.14683 4.52187 8.83441 4.83429L6.2001 7.4686L5.16578 6.43429C4.85336 6.12187 4.34683 6.12187 4.03441 6.43429C3.72199 6.74671 3.72199 7.25324 4.03441 7.56566L5.63441 9.16566C5.94683 9.47808 6.45336 9.47808 6.76578 9.16566L9.96578 5.96566Z"
        fill="#EE5D60"
      />
    </svg>
  );
};

export const BrownCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.73298 3.9991C4.1396 3.96645 6.33232 3.04824 8.0001 1.55554C9.66787 3.04824 11.8606 3.96645 14.2672 3.9991C14.3546 4.51972 14.4001 5.05455 14.4001 5.6C14.4001 9.77988 11.7287 13.3358 8.0001 14.6537C4.27148 13.3358 1.6001 9.77988 1.6001 5.6C1.6001 5.05455 1.64559 4.51972 1.73298 3.9991ZM10.9658 6.96566C11.2782 6.65324 11.2782 6.14671 10.9658 5.83429C10.6534 5.52187 10.1468 5.52187 9.83441 5.83429L7.2001 8.4686L6.16578 7.43429C5.85336 7.12187 5.34683 7.12187 5.03441 7.43429C4.72199 7.74671 4.72199 8.25324 5.03441 8.56566L6.63441 10.1657C6.94683 10.4781 7.45336 10.4781 7.76578 10.1657L10.9658 6.96566Z"
        fill="#EA7E31"
      />
    </svg>
  );
};

export const BlueCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.732984 2.9991C3.1396 2.96645 5.33232 2.04824 7.0001 0.555542C8.66787 2.04824 10.8606 2.96645 13.2672 2.9991C13.3546 3.51972 13.4001 4.05455 13.4001 4.6C13.4001 8.77988 10.7287 12.3358 7.0001 13.6537C3.27148 12.3358 0.600098 8.77988 0.600098 4.6C0.600098 4.05455 0.645589 3.51972 0.732984 2.9991ZM9.96578 5.96566C10.2782 5.65324 10.2782 5.14671 9.96578 4.83429C9.65336 4.52187 9.14683 4.52187 8.83441 4.83429L6.2001 7.4686L5.16578 6.43429C4.85336 6.12187 4.34683 6.12187 4.03441 6.43429C3.72199 6.74671 3.72199 7.25324 4.03441 7.56566L5.63441 9.16566C5.94683 9.47808 6.45336 9.47808 6.76578 9.16566L9.96578 5.96566Z"
        fill="#0391B6"
      />
    </svg>
  );
};

export const DarkBlueCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.732984 2.9991C3.1396 2.96645 5.33232 2.04824 7.0001 0.555542C8.66787 2.04824 10.8606 2.96645 13.2672 2.9991C13.3546 3.51972 13.4001 4.05455 13.4001 4.6C13.4001 8.77988 10.7287 12.3358 7.0001 13.6537C3.27148 12.3358 0.600098 8.77988 0.600098 4.6C0.600098 4.05455 0.645589 3.51972 0.732984 2.9991ZM9.96578 5.96566C10.2782 5.65324 10.2782 5.14671 9.96578 4.83429C9.65336 4.52187 9.14683 4.52187 8.83441 4.83429L6.2001 7.4686L5.16578 6.43429C4.85336 6.12187 4.34683 6.12187 4.03441 6.43429C3.72199 6.74671 3.72199 7.25324 4.03441 7.56566L5.63441 9.16566C5.94683 9.47808 6.45336 9.47808 6.76578 9.16566L9.96578 5.96566Z"
        fill="#365697"
      />
    </svg>
  );
};

export const YellowCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.732984 2.9991C3.1396 2.96645 5.33232 2.04824 7.0001 0.555542C8.66787 2.04824 10.8606 2.96645 13.2672 2.9991C13.3546 3.51972 13.4001 4.05455 13.4001 4.6C13.4001 8.77988 10.7287 12.3358 7.0001 13.6537C3.27148 12.3358 0.600098 8.77988 0.600098 4.6C0.600098 4.05455 0.645589 3.51972 0.732984 2.9991ZM9.96578 5.96566C10.2782 5.65324 10.2782 5.14671 9.96578 4.83429C9.65336 4.52187 9.14683 4.52187 8.83441 4.83429L6.2001 7.4686L5.16578 6.43429C4.85336 6.12187 4.34683 6.12187 4.03441 6.43429C3.72199 6.74671 3.72199 7.25324 4.03441 7.56566L5.63441 9.16566C5.94683 9.47808 6.45336 9.47808 6.76578 9.16566L9.96578 5.96566Z"
        fill="#EEB122"
      />
    </svg>
  );
};

export const PurpleCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.732984 2.9991C3.1396 2.96645 5.33232 2.04824 7.0001 0.555542C8.66787 2.04824 10.8606 2.96645 13.2672 2.9991C13.3546 3.51972 13.4001 4.05455 13.4001 4.6C13.4001 8.77988 10.7287 12.3358 7.0001 13.6537C3.27148 12.3358 0.600098 8.77988 0.600098 4.6C0.600098 4.05455 0.645589 3.51972 0.732984 2.9991ZM9.96578 5.96566C10.2782 5.65324 10.2782 5.14671 9.96578 4.83429C9.65336 4.52187 9.14683 4.52187 8.83441 4.83429L6.2001 7.4686L5.16578 6.43429C4.85336 6.12187 4.34683 6.12187 4.03441 6.43429C3.72199 6.74671 3.72199 7.25324 4.03441 7.56566L5.63441 9.16566C5.94683 9.47808 6.45336 9.47808 6.76578 9.16566L9.96578 5.96566Z"
        fill="#633A86"
      />
    </svg>
  );
};

export const DarkGreenCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.732984 2.9991C3.1396 2.96645 5.33232 2.04824 7.0001 0.555542C8.66787 2.04824 10.8606 2.96645 13.2672 2.9991C13.3546 3.51972 13.4001 4.05455 13.4001 4.6C13.4001 8.77988 10.7287 12.3358 7.0001 13.6537C3.27148 12.3358 0.600098 8.77988 0.600098 4.6C0.600098 4.05455 0.645589 3.51972 0.732984 2.9991ZM9.96578 5.96566C10.2782 5.65324 10.2782 5.14671 9.96578 4.83429C9.65336 4.52187 9.14683 4.52187 8.83441 4.83429L6.2001 7.4686L5.16578 6.43429C4.85336 6.12187 4.34683 6.12187 4.03441 6.43429C3.72199 6.74671 3.72199 7.25324 4.03441 7.56566L5.63441 9.16566C5.94683 9.47808 6.45336 9.47808 6.76578 9.16566L9.96578 5.96566Z"
        fill="#00B052"
      />
    </svg>
  );
};

export const LandingIcon1 = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="19"
      height="20"
      viewBox="0 0 19 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2536_32279)">
        <path
          d="M9.5 0.3125C4.25585 0.3125 0 4.65234 0 10C0 15.3477 4.25585 19.6875 9.5 19.6875C14.7442 19.6875 19 15.3477 19 10C19 4.65234 14.7442 0.3125 9.5 0.3125ZM13.3575 14.5664C13.1966 14.5664 13.097 14.5156 12.9476 14.4258C10.5573 12.957 7.77621 12.8945 5.02964 13.4688C4.88024 13.5078 4.68488 13.5703 4.57379 13.5703C4.20222 13.5703 3.96855 13.2695 3.96855 12.9531C3.96855 12.5508 4.20222 12.3594 4.48952 12.2969C7.62681 11.5898 10.8331 11.6523 13.5681 13.3203C13.8018 13.4727 13.9397 13.6094 13.9397 13.9648C13.9397 14.3203 13.6677 14.5664 13.3575 14.5664ZM14.3879 12.0039C14.1887 12.0039 14.0546 11.9141 13.9167 11.8398C11.5226 10.3945 7.95242 9.8125 4.77681 10.6914C4.59294 10.7422 4.49335 10.793 4.32097 10.793C3.91109 10.793 3.57782 10.4531 3.57782 10.0352C3.57782 9.61719 3.77702 9.33984 4.17157 9.22656C5.23649 8.92188 6.3244 8.69531 7.91794 8.69531C10.404 8.69531 12.8058 9.32422 14.6982 10.4727C15.0085 10.6602 15.131 10.9023 15.131 11.2422C15.1272 11.6641 14.8054 12.0039 14.3879 12.0039ZM15.5754 9.02734C15.3762 9.02734 15.2536 8.97656 15.0813 8.875C12.3538 7.21484 7.47742 6.81641 4.32097 7.71484C4.18306 7.75391 4.01069 7.81641 3.82681 7.81641C3.32117 7.81641 2.93427 7.41406 2.93427 6.89453C2.93427 6.36328 3.25605 6.0625 3.60081 5.96094C4.94919 5.55859 6.45847 5.36719 8.10181 5.36719C10.8982 5.36719 13.8286 5.96094 15.97 7.23438C16.2687 7.41016 16.4641 7.65234 16.4641 8.11719C16.4641 8.64844 16.0427 9.02734 15.5754 9.02734Z"
          fill="#1D242D"
        />
      </g>
      <defs>
        <clipPath id="clip0_2536_32279">
          <rect width="19" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const LandingIcon2 = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.2 3.19981C16.2 2.93018 16.0791 2.67475 15.8706 2.5038C15.6621 2.33286 15.3879 2.26441 15.1235 2.31729L6.12348 4.11729C5.7028 4.20143 5.39999 4.5708 5.39999 4.99981V13.2022C5.11849 13.1359 4.81556 13.0998 4.49999 13.0998C3.00882 13.0998 1.79999 13.9057 1.79999 14.8998C1.79999 15.8939 3.00882 16.6998 4.49999 16.6998C5.99115 16.6998 7.19998 15.8939 7.19999 14.8998V7.53763L14.4 6.09763V11.4022C14.1185 11.3359 13.8156 11.2998 13.5 11.2998C12.0088 11.2998 10.8 12.1057 10.8 13.0998C10.8 14.0939 12.0088 14.8998 13.5 14.8998C14.9912 14.8998 16.2 14.0939 16.2 13.0998V3.19981Z"
        fill="#546881"
      />
    </svg>
  );
};

export const LandingIcon3 = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.00005 16.6998C12.9765 16.6998 16.2 13.4763 16.2 9.4998C16.2 5.52335 12.9765 2.2998 9.00005 2.2998C5.0236 2.2998 1.80005 5.52335 1.80005 9.4998C1.80005 13.4763 5.0236 16.6998 9.00005 16.6998ZM10 5.8998C10 5.34752 9.55233 4.8998 9.00005 4.8998C8.44776 4.8998 8.00005 5.34752 8.00005 5.8998V9.4998C8.00005 9.76502 8.10541 10.0194 8.29294 10.2069L10.8385 12.7525C11.2291 13.143 11.8622 13.143 12.2527 12.7525C12.6433 12.362 12.6433 11.7288 12.2527 11.3383L10 9.08559V5.8998Z"
        fill="#546881"
      />
    </svg>
  );
};

export const LandingIcon4 = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.85446 5.15403C4.26035 3.74814 6.53975 3.74814 7.94563 5.15403L9.00005 6.20844L10.0545 5.15403C11.4604 3.74814 13.7397 3.74814 15.1456 5.15403C16.5515 6.55991 16.5515 8.83931 15.1456 10.2452L9.00005 16.3908L2.85446 10.2452C1.44858 8.83931 1.44858 6.55991 2.85446 5.15403Z"
        fill="#546881"
      />
    </svg>
  );
};

export const LandingIcon5 = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20Z"
        fill="#1D242D"
      />
      <path
        d="M8.55343 6.94072C8.7631 6.82156 8.67575 5.98756 9.68958 5.76639C10.7034 5.54523 11.0179 6.39606 11.0179 6.39606V7.46823C11.0179 7.46823 8.5971 7.52356 7.41794 8.31922C6.23877 9.11489 6.29941 10.6509 6.29941 10.6509C6.29941 10.6509 5.98475 12.5061 8.02975 13.0166C10.0748 13.5271 11.4204 11.9784 11.4204 11.9784C11.6016 12.2664 11.8126 12.5345 12.0498 12.7784C12.3469 13.0507 12.4164 13.0847 12.6091 13.0677C12.8018 13.0507 14.1471 11.9274 14.1298 11.7402C14.1124 11.5531 13.3964 10.4807 13.3964 10.4807V6.07273C13.3964 6.07273 13.1343 5.00473 12.3298 4.48939C11.5253 3.97406 10.1798 4.01289 10.1798 4.01289C9.20061 3.93513 8.22942 4.24019 7.47059 4.86389C6.26459 5.90206 6.56177 6.78706 6.6316 6.82122C6.70143 6.85539 8.34377 7.05989 8.55343 6.94072Z"
        fill="white"
      />
      <path
        d="M11.0526 8.79606C11.0526 8.79606 9.80725 8.77473 9.21741 9.22156C8.62758 9.6684 8.69308 10.5321 8.69308 10.5321C8.69308 10.5321 8.90289 11.5872 9.88156 11.4171C10.8602 11.2469 11.0526 9.88523 11.0526 9.88523V8.79606Z"
        fill="#1D242D"
      />
      <path
        d="M4.56816 13.1697C4.56816 13.1697 6.71816 14.7863 9.49699 15.0078C12.2758 15.2293 14.6007 13.9015 14.6007 13.9015C14.6007 13.9015 14.758 13.9015 14.8103 13.9867C14.8382 14.0695 14.8382 14.1592 14.8103 14.242C14.8103 14.242 13.0103 16.1993 9.65418 16.046C6.29801 15.8927 4.42819 13.425 4.42819 13.425C4.42819 13.425 4.30149 13.246 4.34082 13.1697C4.38016 13.0933 4.56816 13.1697 4.56816 13.1697Z"
        fill="white"
      />
      <path
        d="M13.6396 13.5782C13.6396 13.5782 14.1378 13.5272 14.5135 13.5272C14.7243 13.5253 14.9348 13.5424 15.1426 13.5782C15.1426 13.5782 15.2693 13.6292 15.2126 14.0207C15.1559 14.4122 14.8063 15.0674 14.9154 15.1441C15.0246 15.2207 15.4311 14.8249 15.6488 14.3274C15.8664 13.8299 15.8584 13.2381 15.7886 13.1531C15.7188 13.0681 15.1419 12.9659 14.5826 13.0339C14.0233 13.1019 13.4998 13.5101 13.6396 13.5782Z"
        fill="white"
      />
    </svg>
  );
};

export const LandingIcon6 = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.94955 4.999C4.65698 4.96227 7.1238 3.92929 9.00005 2.25C10.8763 3.92929 13.3431 4.96227 16.0506 4.999C16.1489 5.5847 16.2 6.18638 16.2 6.80002C16.2 11.5024 13.1947 15.5028 9.00005 16.9854C4.80536 15.5028 1.80005 11.5024 1.80005 6.80002C1.80005 6.18638 1.85123 5.5847 1.94955 4.999ZM12.3364 8.33638C12.6879 7.98491 12.6879 7.41506 12.3364 7.06359C11.985 6.71212 11.4151 6.71212 11.0637 7.06359L8.10005 10.0272L6.93644 8.86359C6.58497 8.51212 6.01512 8.51212 5.66365 8.86359C5.31218 9.21506 5.31218 9.78491 5.66365 10.1364L7.46365 11.9364C7.81512 12.2879 8.38497 12.2879 8.73644 11.9364L12.3364 8.33638Z"
        fill="#546881"
      />
    </svg>
  );
};

export const LandingIcon7 = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="18"
      height="19"
      viewBox="0 0 18 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.59995 2.2998C4.09701 2.2998 4.49995 2.70275 4.49995 3.1998V5.09093C5.64303 3.9242 7.23642 3.1998 8.99995 3.1998C11.7444 3.1998 14.0768 4.95419 14.9412 7.39988C15.1069 7.86853 14.8612 8.38272 14.3926 8.54836C13.924 8.714 13.4098 8.46837 13.2441 7.99973C12.6259 6.25072 10.9579 4.9998 8.99995 4.9998C7.52851 4.9998 6.22078 5.70632 5.39937 6.7998H8.09995C8.59701 6.7998 8.99995 7.20275 8.99995 7.6998C8.99995 8.19686 8.59701 8.5998 8.09995 8.5998H3.59995C3.10289 8.5998 2.69995 8.19686 2.69995 7.6998V3.1998C2.69995 2.70275 3.10289 2.2998 3.59995 2.2998ZM3.6073 10.4512C4.07594 10.2856 4.59014 10.5312 4.75578 10.9999C5.37396 12.7489 7.04203 13.9998 8.99995 13.9998C10.4714 13.9998 11.7791 13.2933 12.6005 12.1998L9.89995 12.1998C9.40289 12.1998 8.99995 11.7969 8.99995 11.2998C8.99995 10.8027 9.40289 10.3998 9.89995 10.3998H14.3999C14.6386 10.3998 14.8676 10.4946 15.0363 10.6634C15.2051 10.8322 15.3 11.0611 15.3 11.2998V15.7998C15.3 16.2969 14.897 16.6998 14.4 16.6998C13.9029 16.6998 13.5 16.2969 13.5 15.7998V13.9087C12.3569 15.0754 10.7635 15.7998 8.99995 15.7998C6.25549 15.7998 3.92309 14.0454 3.05867 11.5997C2.89302 11.1311 3.13866 10.6169 3.6073 10.4512Z"
        fill="#546881"
      />
    </svg>
  );
};

export const LandingArrowNext = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="40"
      height="41"
      viewBox="0 0 40 41"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M28 19.5H15.83L21.42 13.91L20 12.5L12 20.5L20 28.5L21.41 27.09L15.83 21.5H28V19.5Z"
        fill="#1D242D"
      />
    </svg>
  );
};

export const LandingArrowPrev = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="24"
      height="25"
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 13.5L16.17 13.5L10.58 19.09L12 20.5L20 12.5L12 4.5L10.59 5.91L16.17 11.5L4 11.5L4 13.5Z"
        fill="#1D242D"
      />
    </svg>
  );
};

export const ChevronRight = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.8137 12.071L8.86372 7.12098C8.68156 6.93238 8.58077 6.67978 8.58305 6.41758C8.58532 6.15538 8.69049 5.90457 8.8759 5.71916C9.06131 5.53375 9.31212 5.42859 9.57432 5.42631C9.83652 5.42403 10.0891 5.52482 10.2777 5.70698L15.9347 11.364C16.1222 11.5515 16.2275 11.8058 16.2275 12.071C16.2275 12.3361 16.1222 12.5905 15.9347 12.778L10.2777 18.435C10.0891 18.6171 9.83652 18.7179 9.57432 18.7157C9.31212 18.7134 9.06131 18.6082 8.8759 18.4228C8.69049 18.2374 8.58532 17.9866 8.58305 17.7244C8.58077 17.4622 8.68156 17.2096 8.86372 17.021L13.8137 12.071Z"
        fill={color}
      />
    </svg>
  );
};

export const ChevronLeft = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="9"
      height="14"
      viewBox="0 0 9 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.25656 7.00006L8.20656 11.9501C8.30207 12.0423 8.37825 12.1526 8.43066 12.2747C8.48307 12.3967 8.51066 12.5279 8.51181 12.6607C8.51296 12.7934 8.48766 12.9251 8.43738 13.048C8.3871 13.1709 8.31285 13.2826 8.21896 13.3765C8.12506 13.4703 8.01341 13.5446 7.89051 13.5949C7.76762 13.6452 7.63594 13.6705 7.50316 13.6693C7.37038 13.6682 7.23916 13.6406 7.11716 13.5882C6.99515 13.5358 6.88481 13.4596 6.79256 13.3641L1.13556 7.70706C0.948089 7.51953 0.842773 7.26522 0.842773 7.00006C0.842773 6.73489 0.948089 6.48059 1.13556 6.29306L6.79256 0.636058C6.98116 0.4539 7.23376 0.353106 7.49596 0.355384C7.75816 0.357663 8.00897 0.462832 8.19438 0.64824C8.37979 0.833648 8.48496 1.08446 8.48723 1.34666C8.48951 1.60885 8.38872 1.86146 8.20656 2.05006L3.25656 7.00006Z"
        fill={color}
      />
    </svg>
  );
};

export const PatientPrevBtn = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="16"
      height="12"
      viewBox="0 0 16 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.64833 11.6485C7.17971 12.1172 6.41991 12.1172 5.95128 11.6485L1.15128 6.84853C0.682647 6.3799 0.682647 5.6201 1.15128 5.15147L5.95128 0.351472C6.41991 -0.117157 7.17971 -0.117157 7.64833 0.351472C8.11696 0.820101 8.11696 1.5799 7.64833 2.04853L4.89686 4.8L13.9998 4.8C14.6625 4.8 15.1998 5.33726 15.1998 6C15.1998 6.66274 14.6625 7.2 13.9998 7.2H4.89686L7.64833 9.95147C8.11696 10.4201 8.11696 11.1799 7.64833 11.6485Z"
        fill="#21272A"
      />
    </svg>
  );
};

export const UserAvatar = ({
  width = 32,
  height = 32,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width={width}
      height={height}
      fill="none"
      viewBox="0 0 24 24"
      className="text-gray-300"
    >
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5" />
      <path
        d="M12 13c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v1h16v-1c0-2.66-5.33-4-8-4z"
        fill="currentColor"
      />
    </svg>
  );
};


export const CopyCard = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="18" height="18" fill="none" viewBox="0 0 20 20">
      <rect
        x="5"
        y="5"
        width="10"
        height="12"
        rx="2"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <rect
        x="8"
        y="3"
        width="7"
        height="12"
        rx="2"
        stroke="currentColor"
        strokeWidth="1.5"
      />
    </svg>
  );
};

export const ViewEyeBtn = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="18" height="18" fill="none" viewBox="0 0 20 20">
      <path
        d="M2 10s2.5-5 8-5 8 5 8 5-2.5 5-8 5-8-5-8-5z"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        d="M7.5 10a2.5 2.5 0 105 0 2.5 2.5 0 00-5 0z"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <line
        x1="3"
        y1="17"
        x2="17"
        y2="3"
        stroke="currentColor"
        strokeWidth="1.5"
      />
    </svg>
  );
};

export const ChallengeUserIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.5" y="0.5" width="23" height="23" rx="11.5" fill="#F2F4F8" />
      <rect x="0.5" y="0.5" width="23" height="23" rx="11.5" stroke="#DDE1E6" />
      <path
        d="M11.9999 11.2C13.3254 11.2 14.3999 10.1255 14.3999 8.80002C14.3999 7.47454 13.3254 6.40002 11.9999 6.40002C10.6744 6.40002 9.5999 7.47454 9.5999 8.80002C9.5999 10.1255 10.6744 11.2 11.9999 11.2Z"
        fill="#1D242D"
      />
      <path
        d="M6.3999 18.4C6.3999 15.3072 8.90711 12.8 11.9999 12.8C15.0927 12.8 17.5999 15.3072 17.5999 18.4H6.3999Z"
        fill="#1D242D"
      />
    </svg>
  );
};

export const PreviewNewProgram = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="98"
      height="68"
      viewBox="0 0 88 66"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xlinkHref="http://www.w3.org/1999/xlink"
    >
      <rect
        y="0.5"
        width="88"
        height="65"
        rx="4"
        fill="url(#pattern0_3798_22858)"
      />
      <defs>
        <pattern
          id="pattern0_3798_22858"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use
            href="#image0_3798_22858"
            transform="matrix(0.005 0 0 0.00676923 0 -0.176923)"
          />
        </pattern>
        <image
          id="image0_3798_22858"
          width="200"
          height="200"
          preserveAspectRatio="none"
          xlinkHref="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
};

export const SixDots = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="10"
      height="17"
      viewBox="0 0 10 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 14.5C4 15.6 3.1 16.5 2 16.5C0.9 16.5 0 15.6 0 14.5C0 13.4 0.9 12.5 2 12.5C3.1 12.5 4 13.4 4 14.5ZM2 6.5C0.9 6.5 0 7.4 0 8.5C0 9.6 0.9 10.5 2 10.5C3.1 10.5 4 9.6 4 8.5C4 7.4 3.1 6.5 2 6.5ZM2 0.5C0.9 0.5 0 1.4 0 2.5C0 3.6 0.9 4.5 2 4.5C3.1 4.5 4 3.6 4 2.5C4 1.4 3.1 0.5 2 0.5ZM8 4.5C9.1 4.5 10 3.6 10 2.5C10 1.4 9.1 0.5 8 0.5C6.9 0.5 6 1.4 6 2.5C6 3.6 6.9 4.5 8 4.5ZM8 6.5C6.9 6.5 6 7.4 6 8.5C6 9.6 6.9 10.5 8 10.5C9.1 10.5 10 9.6 10 8.5C10 7.4 9.1 6.5 8 6.5ZM8 12.5C6.9 12.5 6 13.4 6 14.5C6 15.6 6.9 16.5 8 16.5C9.1 16.5 10 15.6 10 14.5C10 13.4 9.1 12.5 8 12.5Z"
        fill="#1D242D"
      />
    </svg>
  );
};

export const YouTubeIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.812 5.01663H6.145C3.855 5.01663 2 6.85163 2 9.11563V14.8836C2 17.1476 3.856 18.9836 6.145 18.9836H17.812C20.102 18.9836 21.957 17.1476 21.957 14.8836V9.11563C21.957 6.85163 20.101 5.01562 17.812 5.01562V5.01663ZM15.009 12.2796L9.552 14.8546C9.51872 14.8707 9.48192 14.878 9.44503 14.876C9.40815 14.874 9.37237 14.8626 9.34103 14.8431C9.3097 14.8235 9.28382 14.7964 9.2658 14.7641C9.24779 14.7318 9.23822 14.6956 9.238 14.6586V9.34963C9.23867 9.3125 9.24872 9.27614 9.26722 9.24395C9.28573 9.21176 9.31208 9.18477 9.34382 9.1655C9.37556 9.14624 9.41167 9.13532 9.44877 9.13377C9.48587 9.13221 9.52276 9.14008 9.556 9.15663L15.014 11.8916C15.0504 11.9098 15.0809 11.9378 15.102 11.9725C15.1232 12.0071 15.1341 12.0471 15.1336 12.0877C15.1331 12.1283 15.1211 12.168 15.0991 12.2021C15.077 12.2362 15.0458 12.2634 15.009 12.2806V12.2796Z"
        fill="white"
      />
    </svg>
  );
};

export const FacebookIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="10"
      height="20"
      viewBox="0 0 10 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.04598 3.865V6.613H0.0319824V9.973H2.04598V19.959H6.17998V9.974H8.95498C8.95498 9.974 9.21498 8.363 9.34098 6.601H6.19698V4.303C6.19698 3.96 6.64698 3.498 7.09298 3.498H9.34698V0H6.28298C1.94298 0 2.04598 3.363 2.04598 3.865Z"
        fill="white"
      />
    </svg>
  );
};

export const EmailIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="14"
      viewBox="0 0 20 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.598 2L9.345 7.12C9.52813 7.28306 9.76479 7.37316 10.01 7.37316C10.2552 7.37316 10.4919 7.28306 10.675 7.12L16.423 2H3.598ZM18 3.273L12.006 8.614C11.4565 9.10374 10.7461 9.37436 10.01 9.37436C9.27392 9.37436 8.56352 9.10374 8.014 8.614L2 3.254V12H18V3.273ZM2 0H18C18.5304 0 19.0391 0.210714 19.4142 0.585786C19.7893 0.960859 20 1.46957 20 2V12C20 12.5304 19.7893 13.0391 19.4142 13.4142C19.0391 13.7893 18.5304 14 18 14H2C1.46957 14 0.960859 13.7893 0.585786 13.4142C0.210714 13.0391 0 12.5304 0 12V2C0 1.46957 0.210714 0.960859 0.585786 0.585786C0.960859 0.210714 1.46957 0 2 0V0Z"
        fill="#546881"
      />
    </svg>
  );
};

export const InstagramIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.017 2H7.947C6.37015 2.00185 4.85844 2.62914 3.74353 3.74424C2.62862 4.85933 2.00159 6.37115 2 7.948L2 16.018C2.00185 17.5948 2.62914 19.1066 3.74424 20.2215C4.85933 21.3364 6.37115 21.9634 7.948 21.965H16.018C17.5948 21.9631 19.1066 21.3359 20.2215 20.2208C21.3364 19.1057 21.9634 17.5938 21.965 16.017V7.947C21.9631 6.37015 21.3359 4.85844 20.2208 3.74353C19.1057 2.62862 17.5938 2.00159 16.017 2V2ZM19.957 16.017C19.957 16.5344 19.8551 17.0468 19.6571 17.5248C19.4591 18.0028 19.1689 18.4371 18.803 18.803C18.4371 19.1689 18.0028 19.4591 17.5248 19.6571C17.0468 19.8551 16.5344 19.957 16.017 19.957H7.947C6.90222 19.9567 5.90032 19.5415 5.16165 18.8026C4.42297 18.0638 4.008 17.0618 4.008 16.017V7.947C4.00827 6.90222 4.42349 5.90032 5.16235 5.16165C5.90122 4.42297 6.90322 4.008 7.948 4.008H16.018C17.0628 4.00827 18.0647 4.42349 18.8034 5.16235C19.542 5.90122 19.957 6.90322 19.957 7.948V16.018V16.017Z"
        fill="white"
      />
      <path
        d="M11.982 6.81934C10.6134 6.82145 9.30154 7.36612 8.33391 8.33394C7.36627 9.30176 6.82186 10.6138 6.82001 11.9823C6.82159 13.3513 7.36603 14.6637 8.33391 15.6317C9.30179 16.5998 10.6141 17.1445 11.983 17.1463C13.3521 17.1447 14.6647 16.6002 15.6328 15.6321C16.6008 14.664 17.1454 13.3514 17.147 11.9823C17.1449 10.6134 16.5999 9.30122 15.6317 8.33353C14.6634 7.36584 13.3509 6.82166 11.982 6.82034V6.81934ZM11.982 15.1383C11.1452 15.1383 10.3428 14.8059 9.75109 14.2143C9.15941 13.6226 8.82701 12.8201 8.82701 11.9833C8.82701 11.1466 9.15941 10.3441 9.75109 9.75241C10.3428 9.16074 11.1452 8.82834 11.982 8.82834C12.8188 8.82834 13.6213 9.16074 14.2129 9.75241C14.8046 10.3441 15.137 11.1466 15.137 11.9833C15.137 12.8201 14.8046 13.6226 14.2129 14.2143C13.6213 14.8059 12.8188 15.1383 11.982 15.1383Z"
        fill="white"
      />
      <path
        d="M17.156 8.09509C17.8392 8.09509 18.393 7.54127 18.393 6.85809C18.393 6.17492 17.8392 5.62109 17.156 5.62109C16.4728 5.62109 15.919 6.17492 15.919 6.85809C15.919 7.54127 16.4728 8.09509 17.156 8.09509Z"
        fill="white"
      />
    </svg>
  );
};

export const LinkedInIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.959 11.7194V19.0984H15.681V12.2134C15.681 10.4834 15.062 9.30337 13.514 9.30337C12.332 9.30337 11.628 10.0994 11.319 10.8684C11.206 11.1434 11.177 11.5264 11.177 11.9114V19.0984H6.897C6.897 19.0984 6.955 7.43837 6.897 6.22937H11.177V8.05337L11.149 8.09537H11.177V8.05337C11.745 7.17837 12.76 5.92737 15.033 5.92737C17.848 5.92737 19.959 7.76737 19.959 11.7194ZM2.421 0.0263672C0.958 0.0263672 0 0.986367 0 2.24937C0 3.48437 0.93 4.47337 2.365 4.47337H2.393C3.886 4.47337 4.813 3.48437 4.813 2.24937C4.787 0.986367 3.887 0.0263672 2.422 0.0263672H2.421ZM0.254 19.0984H4.532V6.22937H0.254V19.0984Z"
        fill="white"
      />
    </svg>
  );
};

export const TwitterIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="17"
      viewBox="0 0 20 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 1.90668C19.2504 2.23405 18.4565 2.44871 17.644 2.54368C18.4968 2.04291 19.138 1.24878 19.448 0.309676C18.64 0.780004 17.7587 1.11128 16.841 1.28968C16.4545 0.884881 15.9897 0.563063 15.4748 0.343854C14.9598 0.124645 14.4056 0.0126411 13.846 0.0146757C11.58 0.0146757 9.743 1.82468 9.743 4.05468C9.743 4.37068 9.779 4.67968 9.849 4.97468C8.22358 4.89737 6.63212 4.48208 5.17617 3.75531C3.72022 3.02855 2.43176 2.00626 1.393 0.753676C1.02883 1.36808 0.837415 2.06946 0.839 2.78368C0.839697 3.45164 1.00683 4.10891 1.32529 4.69607C1.64375 5.28323 2.1035 5.78179 2.663 6.14668C2.01248 6.12568 1.37602 5.952 0.805 5.63968V5.68968C0.805 7.64768 2.22 9.28068 4.095 9.65268C3.74261 9.74617 3.37958 9.79357 3.015 9.79368C2.75 9.79368 2.493 9.76868 2.242 9.71868C2.51008 10.5266 3.02311 11.231 3.70982 11.734C4.39653 12.237 5.22284 12.5137 6.074 12.5257C4.61407 13.6502 2.82182 14.2577 0.979 14.2527C0.647 14.2527 0.321 14.2327 0 14.1967C1.88125 15.3874 4.06259 16.018 6.289 16.0147C13.836 16.0147 17.962 9.85768 17.962 4.51868L17.948 3.99568C18.7529 3.42935 19.4481 2.72152 20 1.90668Z"
        fill="white"
      />
    </svg>
  );
};

export const ArrowRightIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.586 5.65692L6.636 1.70692C6.45384 1.51832 6.35305 1.26571 6.35533 1.00352C6.3576 0.741321 6.46277 0.490508 6.64818 0.3051C6.83359 0.119692 7.0844 0.0145233 7.3466 0.0122448C7.6088 0.00996641 7.8614 0.110761 8.05 0.292919L13.707 5.94992C13.8002 6.04257 13.8741 6.15273 13.9246 6.27407C13.9751 6.3954 14.001 6.52551 14.001 6.65692C14.001 6.78833 13.9751 6.91844 13.9246 7.03977C13.8741 7.16111 13.8002 7.27127 13.707 7.36392L8.05 13.0209C7.95775 13.1164 7.84741 13.1926 7.7254 13.245C7.6034 13.2974 7.47218 13.325 7.3394 13.3262C7.20662 13.3273 7.07494 13.302 6.95205 13.2517C6.82915 13.2015 6.7175 13.1272 6.62361 13.0333C6.52971 12.9394 6.45546 12.8278 6.40518 12.7049C6.3549 12.582 6.3296 12.4503 6.33075 12.3175C6.3319 12.1847 6.35949 12.0535 6.4119 11.9315C6.46431 11.8095 6.54049 11.6992 6.636 11.6069L10.586 7.65692H1C0.734784 7.65692 0.48043 7.55156 0.292893 7.36403C0.105357 7.17649 0 6.92214 0 6.65692C0 6.3917 0.105357 6.13735 0.292893 5.94981C0.48043 5.76228 0.734784 5.65692 1 5.65692H10.586Z" fill="white" />
    </svg>
  );
};

export const FilledUserIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M5.9999 5.1999C7.32539 5.1999 8.3999 4.12539 8.3999 2.7999C8.3999 1.47442 7.32539 0.399902 5.9999 0.399902C4.67442 0.399902 3.5999 1.47442 3.5999 2.7999C3.5999 4.12539 4.67442 5.1999 5.9999 5.1999Z" fill="#1D242D" />
      <path d="M0.399902 12.3999C0.399902 9.30711 2.90711 6.7999 5.9999 6.7999C9.0927 6.7999 11.5999 9.30711 11.5999 12.3999H0.399902Z" fill="#1D242D" />
    </svg>
  );
};

export const PatientUserIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" rx="20" fill="#F2F4F8" />
      <path
        d="M16.534 20.07C16.6569 20.0175 16.7892 19.9903 16.9228 19.9898C17.0565 19.9893 17.1889 20.0156 17.3122 20.0672C17.4355 20.1188 17.5473 20.1945 17.6408 20.29C17.7344 20.3855 17.8078 20.4988 17.8568 20.6231C17.9058 20.7475 17.9294 20.8804 17.9262 21.014C17.9229 21.1477 17.8929 21.2793 17.8379 21.4011C17.7829 21.523 17.7041 21.6325 17.606 21.7234C17.508 21.8142 17.3927 21.8845 17.267 21.93C16.5985 22.1934 16.0248 22.6519 15.6205 23.2458C15.2162 23.8397 15 24.5415 15 25.26V27C15 27.2652 15.1054 27.5196 15.2929 27.7071C15.4804 27.8946 15.7348 28 16 28H24C24.2652 28 24.5196 27.8946 24.7071 27.7071C24.8946 27.5196 25 27.2652 25 27V25.353C25.0001 24.6115 24.7749 23.8874 24.3541 23.2768C23.9334 22.6662 23.337 22.1979 22.644 21.934C22.5175 21.8901 22.4012 21.8213 22.3018 21.7316C22.2024 21.6419 22.1221 21.5332 22.0655 21.4119C22.009 21.2905 21.9773 21.1591 21.9725 21.0253C21.9677 20.8916 21.9897 20.7582 22.0374 20.6331C22.0851 20.508 22.1574 20.3938 22.25 20.2972C22.3427 20.2006 22.4538 20.1235 22.5767 20.0706C22.6997 20.0177 22.832 19.9901 22.9659 19.9893C23.0998 19.9885 23.2324 20.0146 23.356 20.066C24.4276 20.4742 25.3499 21.1983 26.0006 22.1425C26.6514 23.0867 26.9999 24.2063 27 25.353V27C27 27.7956 26.6839 28.5587 26.1213 29.1213C25.5587 29.6839 24.7956 30 24 30H16C15.2044 30 14.4413 29.6839 13.8787 29.1213C13.3161 28.5587 13 27.7956 13 27V25.26C13.0001 24.1402 13.3373 23.0463 13.9676 22.1206C14.5978 21.195 15.4921 20.4805 16.534 20.07ZM20 10C21.0609 10 22.0783 10.4214 22.8284 11.1716C23.5786 11.9217 24 12.9391 24 14V16C24 17.0609 23.5786 18.0783 22.8284 18.8284C22.0783 19.5786 21.0609 20 20 20C18.9391 20 17.9217 19.5786 17.1716 18.8284C16.4214 18.0783 16 17.0609 16 16V14C16 12.9391 16.4214 11.9217 17.1716 11.1716C17.9217 10.4214 18.9391 10 20 10V10ZM20 12C19.4696 12 18.9609 12.2107 18.5858 12.5858C18.2107 12.9609 18 13.4696 18 14V16C18 16.5304 18.2107 17.0391 18.5858 17.4142C18.9609 17.7893 19.4696 18 20 18C20.5304 18 21.0391 17.7893 21.4142 17.4142C21.7893 17.0391 22 16.5304 22 16V14C22 13.4696 21.7893 12.9609 21.4142 12.5858C21.0391 12.2107 20.5304 12 20 12Z"
        fill="#C1C7CD"
      />
    </svg>
  );
};

export const ThreeDots = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="20" height="6" viewBox="0 0 20 6" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 4C3.26522 4 3.51957 3.89464 3.70711 3.70711C3.89464 3.51957 4 3.26522 4 3C4 2.73478 3.89464 2.48043 3.70711 2.29289C3.51957 2.10536 3.26522 2 3 2C2.73478 2 2.48043 2.10536 2.29289 2.29289C2.10536 2.48043 2 2.73478 2 3C2 3.26522 2.10536 3.51957 2.29289 3.70711C2.48043 3.89464 2.73478 4 3 4ZM3 6C2.20435 6 1.44129 5.68393 0.87868 5.12132C0.31607 4.55871 0 3.79565 0 3C0 2.20435 0.31607 1.44129 0.87868 0.87868C1.44129 0.31607 2.20435 0 3 0C3.79565 0 4.55871 0.31607 5.12132 0.87868C5.68393 1.44129 6 2.20435 6 3C6 3.79565 5.68393 4.55871 5.12132 5.12132C4.55871 5.68393 3.79565 6 3 6ZM17 6C16.2044 6 15.4413 5.68393 14.8787 5.12132C14.3161 4.55871 14 3.79565 14 3C14 2.20435 14.3161 1.44129 14.8787 0.87868C15.4413 0.31607 16.2044 0 17 0C17.7956 0 18.5587 0.31607 19.1213 0.87868C19.6839 1.44129 20 2.20435 20 3C20 3.79565 19.6839 4.55871 19.1213 5.12132C18.5587 5.68393 17.7956 6 17 6ZM17 4C17.2652 4 17.5196 3.89464 17.7071 3.70711C17.8946 3.51957 18 3.26522 18 3C18 2.73478 17.8946 2.48043 17.7071 2.29289C17.5196 2.10536 17.2652 2 17 2C16.7348 2 16.4804 2.10536 16.2929 2.29289C16.1054 2.48043 16 2.73478 16 3C16 3.26522 16.1054 3.51957 16.2929 3.70711C16.4804 3.89464 16.7348 4 17 4ZM10 6C9.20435 6 8.44129 5.68393 7.87868 5.12132C7.31607 4.55871 7 3.79565 7 3C7 2.20435 7.31607 1.44129 7.87868 0.87868C8.44129 0.31607 9.20435 0 10 0C10.7956 0 11.5587 0.31607 12.1213 0.87868C12.6839 1.44129 13 2.20435 13 3C13 3.79565 12.6839 4.55871 12.1213 5.12132C11.5587 5.68393 10.7956 6 10 6ZM10 4C10.2652 4 10.5196 3.89464 10.7071 3.70711C10.8946 3.51957 11 3.26522 11 3C11 2.73478 10.8946 2.48043 10.7071 2.29289C10.5196 2.10536 10.2652 2 10 2C9.73478 2 9.48043 2.10536 9.29289 2.29289C9.10536 2.48043 9 2.73478 9 3C9 3.26522 9.10536 3.51957 9.29289 3.70711C9.48043 3.89464 9.73478 4 10 4Z" fill="#697077" />
    </svg>
  );
};

export const PlusIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 6.75H6.75V12H5.25V6.75H0V5.25H5.25V0H6.75V5.25H12V6.75Z"
        fill="#546881"
      />
    </svg>
  );
};

export const ProgressBtn = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.95 11C19.449 16.0529 15.185 20 10 20C4.477 20 0 15.523 0 9.99995C0 4.81495 3.947 0.55095 9 0.0499502V2.06195C6.98271 2.31863 5.13885 3.33382 3.84319 4.90117C2.54752 6.46852 1.89728 8.47042 2.02462 10.5C2.15196 12.5295 3.04733 14.4345 4.52874 15.8276C6.01016 17.2207 7.96645 17.9974 10 18C11.9486 17.9999 13.8302 17.2888 15.2917 15.9999C16.7533 14.7111 17.6942 12.9333 17.938 11H19.951H19.95ZM19.95 8.99995H17.938C17.7154 7.23756 16.9129 5.59929 15.6568 4.34319C14.4007 3.08709 12.7624 2.28454 11 2.06195V0.0489502C13.2951 0.280186 15.4398 1.29756 17.0709 2.92877C18.7019 4.55999 19.719 6.70479 19.95 8.99995Z"
        fill="#001D6C"
      />
    </svg>
  );
};

export const ProgressCheck = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.4853 16.73C10.354 16.7303 10.224 16.7046 10.1026 16.6545C9.98131 16.6044 9.87108 16.5308 9.77826 16.438L5.53626 12.195C5.44068 12.1028 5.36442 11.9925 5.31193 11.8706C5.25944 11.7486 5.23176 11.6174 5.23051 11.4846C5.22927 11.3519 5.25448 11.2202 5.30467 11.0972C5.35486 10.9743 5.42904 10.8626 5.52286 10.7686C5.61669 10.6747 5.72829 10.6004 5.85115 10.55C5.974 10.4996 6.10566 10.4742 6.23844 10.4753C6.37122 10.4763 6.50246 10.5038 6.6245 10.5562C6.74654 10.6085 6.85694 10.6846 6.94926 10.78L10.4843 14.315L16.8493 7.95202C17.0368 7.76438 17.2911 7.65891 17.5564 7.65881C17.8217 7.65872 18.0761 7.76401 18.2638 7.95152C18.4514 8.13902 18.5569 8.39339 18.557 8.65866C18.5571 8.92393 18.4518 9.17838 18.2643 9.36602L11.1923 16.438C11.0994 16.5308 10.9892 16.6044 10.8679 16.6545C10.7465 16.7046 10.6165 16.7303 10.4853 16.73Z" fill="#25A249" />
    </svg>

  );
};

export const LockIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="14" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M2.00039 6.19998V4.59998C2.00039 2.39084 3.79125 0.599976 6.00039 0.599976C8.20953 0.599976 10.0004 2.39084 10.0004 4.59998V6.19998C10.884 6.19998 11.6004 6.91632 11.6004 7.79998V11.8C11.6004 12.6836 10.884 13.4 10.0004 13.4H2.00039C1.11674 13.4 0.400391 12.6836 0.400391 11.8V7.79998C0.400391 6.91632 1.11674 6.19998 2.00039 6.19998ZM8.40039 4.59998V6.19998H3.60039V4.59998C3.60039 3.27449 4.67491 2.19998 6.00039 2.19998C7.32587 2.19998 8.40039 3.27449 8.40039 4.59998Z" fill="#21272A" />
    </svg>
  );
};

export const ReviewIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M7.99961 14.4C11.5342 14.4 14.3996 11.5346 14.3996 7.99998C14.3996 4.46535 11.5342 1.59998 7.99961 1.59998C4.46499 1.59998 1.59961 4.46535 1.59961 7.99998C1.59961 11.5346 4.46499 14.4 7.99961 14.4ZM7.64337 5.73434C7.39788 5.57068 7.08225 5.55542 6.82212 5.69464C6.562 5.83385 6.39961 6.10494 6.39961 6.39998V9.59998C6.39961 9.89501 6.562 10.1661 6.82212 10.3053C7.08225 10.4445 7.39788 10.4293 7.64337 10.2656L10.0434 8.66562C10.2659 8.51724 10.3996 8.26746 10.3996 7.99998C10.3996 7.73249 10.2659 7.48271 10.0434 7.33434L7.64337 5.73434Z" fill="#21272A" />
    </svg>

  );
};

export const PassedIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M6.99961 13.4C10.5342 13.4 13.3996 10.5346 13.3996 6.99998C13.3996 3.46535 10.5342 0.599976 6.99961 0.599976C3.46499 0.599976 0.599609 3.46535 0.599609 6.99998C0.599609 10.5346 3.46499 13.4 6.99961 13.4ZM9.96529 5.96566C10.2777 5.65324 10.2777 5.14671 9.96529 4.83429C9.65287 4.52187 9.14634 4.52187 8.83392 4.83429L6.19961 7.4686L5.16529 6.43429C4.85288 6.12187 4.34634 6.12187 4.03392 6.43429C3.7215 6.74671 3.7215 7.25324 4.03392 7.56566L5.63392 9.16566C5.94634 9.47808 6.45288 9.47808 6.76529 9.16566L9.96529 5.96566Z" fill="#47AEA9" />
    </svg>

  );
};


export const UploadIcon = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 12L3 12.75C3 13.9926 4.00736 15 5.25 15L12.75 15C13.9926 15 15 13.9926 15 12.75L15 12M12 6L9 3M9 3L6 6M9 3L9 12" stroke="#546881" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export const LockOpen = ({
  width = 0,
  height = 0,
  color = "#253B80",
}: CardIconProps) => {
  return (
    <svg width="12" height="14" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M6.00039 0.599976C3.79125 0.599976 2.00039 2.39084 2.00039 4.59998V6.19998C1.11674 6.19998 0.400391 6.91632 0.400391 7.79998V11.8C0.400391 12.6836 1.11674 13.4 2.00039 13.4H10.0004C10.884 13.4 11.6004 12.6836 11.6004 11.8V7.79998C11.6004 6.91632 10.884 6.19998 10.0004 6.19998H3.60039V4.59998C3.60039 3.27449 4.67491 2.19998 6.00039 2.19998C7.1176 2.19998 8.05836 2.96407 8.32483 3.99938C8.43496 4.42726 8.8711 4.68485 9.29898 4.57473C9.72687 4.4646 9.98446 4.02845 9.87433 3.60057C9.43033 1.87551 7.86533 0.599976 6.00039 0.599976Z" fill="#1D242D" />
    </svg>
  );
};
