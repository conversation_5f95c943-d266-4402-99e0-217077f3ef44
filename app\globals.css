@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  :root {
    --primary: #47AEA9;
    --secondary: #EDBA29;
    --neutral: #1D242D;
    --netural-40: #E2E8F0;
    --netural-90: #344055;
    --netural-100: #121212;
    --netural-20: #F8FAFC;
    --neutral-light: #FDF8EA;
    --wheel-physical: #8BBB4B;
    --wheel-love: #CC1F70;
    --wheel-social: #EE5D60;
    --wheel-emotional: #814679;
    --wheel-family: #324D88;
    --wheel-work: #2472AB;
    --wheel-creative: #2BACA4;
    --wheel-spiritual: #34847D;
    --wheel-financial: #338F5E;
    --core-green: #F6FBEA;
    --core-green-hover: #F1F9E0;
    --core-green-active: #E2F2BF;
    --core-green-disabled: #F2F9F2;
    --core-green-normal: #A1D431;
    --core-green-normal-hover: #91BF2C;
    --core-green-normal-active: #81AA27;
    --influencer-gold: #FDF8EA;
    --influencer-gold-hover: #FCF5DF;
    --influencer-gold-active: #F9EABD;
    --influencer-gold-normal: #EDBA29;
    --influencer-gold-normal-hover: #D5A725;
    --influencer-gold-normal-active: #BE9521;
    --light-blue: #FFFFFF;
    --light-blue-hover: #FCFDFD;
    --light-blue-active: #F5F7F9;
    --light-blue-light: #FCFDFD;
    --light-blue-light-hover: #F5F7F9;
    --light-blue-light-active: #F0F3F6;
    --light-blue-normal: #F9FAFB;
    --light-blue-normal-hover: #F2F4F7;
    --light-blue-normal-active: #EBEEF2;
    --dark-blue: #B2BBC6;
    --dark-blue-hover: #A3ADBB;
    --dark-blue-active: #909DAD;
    --dark-blue-normal: #546881;
    --dark-blue-normal-hover: #47586E;
    --dark-blue-normal-active: #3D4C5E;
    --paragraph-content: #21272A;
    --landing-background: #F2F4F8;
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: #DDE1E6;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --radius: 4px;
    --content-color: #121619;
    --pagenation-color: #000000;
    --pagenation-hover-color: #CCCCCC;
    --cool-grey-400: #A2A9B0;
    --cool-gray-60: #697077;
    --core-green-dark: #799F25;
    --core-green-dark-hover: #617F1D;
    --core-green-dark-active: #485F16;
    --core-green-darker: #384A11;
    --influencer-gold-dark: #B28C1F;
    --influencer-gold-dark-hover: #8E7019;
    --influencer-gold-dark-active: #6B5412;
    --influencer-gold-darker: #473912;
    --light-blue-dark: #F6F8F9;
    --dark-blue-dark: #1D242C;
    --dark-blue-dark-hover: #151A20;
    --dark-blue-dark-active: #090B0E;
    --darker-blue: #001D6C;
    --medium-gray: #C1C7CD;
    --old-blue: #243247;
    --dark-blue-background: #C1CAD7;
    --old-n30: #46464C;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-paragraphContent font-roboto;
  }
}

/* Thin scrollbar for all browsers */
* {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* For WebKit browsers (Chrome, Safari) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}