import { NotificationIcon } from "@/utils/icons";
import React, { useRef, useEffect, useState } from "react";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";

interface Notification {
  id: number;
  user: string;
  action: string;
  target: string;
  time: string;
  unread?: boolean;
  showResults?: boolean;
  mention?: boolean;
}

interface NotificationDrawerProps {
  open: boolean;
  onClose: () => void;
  notifications: Notification[];
}

export function NotificationDrawer({ open, onClose, notifications }: NotificationDrawerProps) {
  const drawerRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<"all" | "mentions">("all");
  const router = useRouter();

  // Close on click outside
  useEffect(() => {
    function handleClick(event: MouseEvent) {
      if (drawerRef.current && !drawerRef.current.contains(event.target as Node)) {
        onClose();
      }
    }
    if (open) {
      document.addEventListener("mousedown", handleClick);
    }
    return () => {
      document.removeEventListener("mousedown", handleClick);
    };
  }, [open, onClose]);

  if (!open) return null;

  // Filter notifications based on tab
  const filteredNotifications =
    activeTab === "all"
      ? notifications
      : notifications.filter((n) => n.mention);

  // Count of unread notifications
  const unreadCount = notifications.filter(n => n.unread).length;

  // Handle settings icon click
  const handleSettingsClick = () => {
    // Option 1: Use query param
    router.push("/dashboard/account?tab=notifications");
    // Option 2: Use localStorage flag (uncomment if you want to use this instead)
    // localStorage.setItem("accountTab", "notifications");
    // router.push("/dashboard/account");
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50">
      {/* Overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-10" />
      {/* Drawer */}
      <div
        ref={drawerRef}
        className="absolute top-36 left-52 max-md:left-0 max-md:top-0 max-md:w-full w-[440px] max-w-full bg-white rounded-xl shadow-lg border border-gray-200"
      >
        <div className="flex items-center justify-between px-6 py-6">
          <div className="text-title-md font-semibold text-neutral">Notifications</div>
          <div className="flex items-center gap-2">
            <button className="text-label-large text-darkBlueNormal" onClick={onClose}>
              Mark all as read
            </button>
            <button onClick={handleSettingsClick} className="p-0 m-0"><NotificationIcon /></button>
          </div>
        </div>
        <div className="flex border-b px-6">
          <button
            className={`py-2 px-6 border-b-2 text-darkBlueDarker font-semibold text-title-small ${
              activeTab === "all"
                ? "border-blue-600"
                : "border-transparent text-gray-500"
            }`}
            onClick={() => setActiveTab("all")}
          >
            All <span className="ml-1 rounded-full bg-lightBlueNormalHover text-body-small font-semibold px-3 py-1">{unreadCount}</span>
          </button>
          <button
            className={`py-2 px-4 border-b-2 text-darkBlueDarker font-semibold text-title-small ${
              activeTab === "mentions"
                ? "border-blue-600"
                : "border-transparent text-gray-500"
            }`}
            onClick={() => setActiveTab("mentions")}
          >
            Mentions
          </button>
        </div>
        <div className="max-h-[650px] overflow-y-auto">
          {filteredNotifications.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-400">No notifications.</div>
          ) : (
            filteredNotifications.map((n) => (
              <div
                key={n.id}
                className={`flex gap-3 px-6 py-6 border-b border-gray-200 ${n.unread ? "bg-[#EDF3FF]" : ""}`}
              >
                {/* Blue dot and avatar */}
                <div className="flex pt-1 items-start">
                  <div className="flex gap-2 items-center justify-center">
                    {n.unread && (
                      <span className="mb-1 w-2 h-2 rounded-full bg-blue-500 inline-block" />
                    )}
                    <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center font-bold text-lg text-gray-600">
                      SJ
                    </div>
                  </div>
                </div>
                {/* Main content and right-aligned time/menu */}
                <div className="flex-1 flex flex-col justify-center">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="text-body-medium  text-neutral">
                        <span className="font-semibold">{n.user}</span> {n.action} <span className="font-semibold text-neutral">{n.target}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                  {n.showResults && (
                    <Button variant="netural" className="h-8 px-6 rounded text-title-small mt-2"><span className="text-lightBlue">View Results</span></Button>
                  )}
                  </div>  
                </div>
                <div className="flex flex-col items-end gap-1 min-w-[40px]">
                  <span className="text-xs text-gray-400">{n.time}</span>
                  <div className="text-gray-400 hover:text-gray-600">•••</div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
