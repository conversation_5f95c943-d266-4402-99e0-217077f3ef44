"use client";

import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { EyeIcon, EyeOffIcon } from "lucide-react";

export default function NewPasswordPage() {
  const router = useRouter();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    setTimeout(() => {
      setIsLoading(false);
      router.push("/login");
    }, 1500);
  };

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 relative">
          <Image
            src="/auth/forgot-password.svg"
            alt="People in a golf cart"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - New Password Form */}
        <div className="w-full md:w-1/2 flex items-center justify-end">
          <div className="w-full max-w-[640px] lg:pr-20 max-xl:px-5">
            <div className="max-md:text-center max-md:pt-10">
              <h1 className="text-display-md mb-2 max-md:text-3xl">Create a new password</h1>
              <p className="text-body-lg text-paragraphContent mb-8">
                Your new password must be different to previously used passwords,
                and be a combination of minimum 8 letters, numbers, and symbols.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6 mb-5">
              <div className="mb-10">
                <div className="space-y-1 mt-8">
                  <label htmlFor="password" className="text-body-medium text-paragraphContent">
                    New Password
                  </label>
                  <div className="relative h-12">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="bg-landingBackground border-0 border-b-2 border-medium-gray rounded-none shadow-none h-full focus-visible:ring-0 focus-visible:border-gray-500 pl-2 pr-10"
                      disabled={isLoading}
                      placeholder=""
                      required
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-paragraphContent focus:outline-none"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOffIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>

                <div className="space-y-1 mt-6">
                  <label htmlFor="confirmPassword" className="text-body-medium text-paragraphContent">
                    Confirm New Password
                  </label>
                  <div className="relative h-12">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      className="bg-landingBackground border-0 border-b-2 border-medium-gray rounded-none shadow-none h-full focus-visible:ring-0 focus-visible:border-gray-500 pl-2 pr-10"
                      disabled={isLoading}
                      placeholder=""
                      required
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-paragraphContent focus:outline-none"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOffIcon className="h-5 w-5" />
                      ) : (
                        <EyeIcon className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  variant="netural"
                  className="w-full font-normal h-11 rounded-sm mt-5"
                  disabled={isLoading}
                >
                  {isLoading ? "Resetting..." : "Reset Password"}
                </Button>
              </div>

              <div className="text-sm border-t border-gray-200 pt-10">
                Don't want to reset your password? <Link href="/login" className="font-medium underline">Back to Log In</Link>
              </div>

              <div className="text-sm">
                No account yet? <Link href="/signup" className="font-medium underline">Sign Up</Link>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}

