import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  LightRed<PERSON><PERSON><PERSON>,
  BrownCheck,
  BlueCheck,
  DarkBlueCheck,
  YellowCheck,
  PurpleCheck,
  DarkGreenCheck,
} from "@/utils/icons";

interface AreaIconProps {
  area: string;
}

const AREA_ICON_MAP: Record<string, React.ReactNode> = {
  Finance: <GreenCheck />,
  "Love/Relationships": <RedCheck />,
  Family: <LightRedCheck />,
  Creative: <BrownCheck />,
  Physical: <BlueCheck />,
  Social: <BlueCheck />,
  Professional: <DarkBlueCheck />,
  Emotional: <YellowCheck />,
  Spiritual: <PurpleCheck />,
  // Add more if needed
};

export function AreaIcon({ area }: AreaIconProps) {
  return (
    <span className="border border-gray-300 rounded-full p-1 bg-[#edf1f5] mx-0.5 inline-flex items-center justify-center">
      {AREA_ICON_MAP[area] || <DarkGreenCheck />}
    </span>
  );
} 