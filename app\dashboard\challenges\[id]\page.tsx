"use client";

import { useRouter, useParams } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { AreaIcon } from "@/components/dashboard/AreaIcon";
import { UserIcon, ChallengeUserIcon, PatientPrevBtn } from "@/utils/icons";
import { CHALLENGES } from "@/lib/challenges_mockup";
import { useEffect, useState } from "react";
import { ChallengeDetailSkeleton } from "@/components/challenges/ChallengeDetailSkeleton";
import 'react-loading-skeleton/dist/skeleton.css';
import Link from "next/link";

export default function ChallengeDetailPage() {
  const router = useRouter();
  const params = useParams();
  const id = Number(params?.id);
  const [challenge, setChallenge] = useState<any>(null);

  useEffect(() => {
    const found = CHALLENGES.find((c: any) => c.id === id);
    setChallenge(found || null);
  }, [id]);

  if (!challenge) {
    return (
        <div className="w-full">
          <div className="mb-4 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">My Challenges</h1>
            <a
              href="/dashboard/challenges"
              className="flex items-center text-sm text-gray-700 hover:underline mt-6 sm:mt-11 text-[#21272A] pb-3 mb-4 sm:mb-6 border-b border-gray-200"
            >
              <PatientPrevBtn />
              <span className="ml-2 text-title-medium text-paragraphContent">Back to All Challenges</span>
            </a>
          </div>
          <ChallengeDetailSkeleton />
        </div>
    );
  }

  return (
      <div className="w-full">
        <div className="mb-4">
          <h1 className="text-display-md text-paragraphContent">My Challenges</h1>
          <Link
            href="/dashboard/challenges"
            className="flex items-center mt-8 pb-3 mb-4  border-b border-gray-200"
          >
            <PatientPrevBtn />
            <span className="ml-2 text-title-medium text-paragraphContent">Back to All Challenges</span>
          </Link>
        </div>
        <div className="flex justify-between max-md:flex-col-reverse gap-8">
          {/* Steps List */}
          <div className="w-full">
            <div className="flex flex-col gap-1">
              {challenge.stepList.map((step: string, idx: number) => (
                <div
                  key={idx}
                  className="flex items-center justify-between bg-white rounded-lg shadow-sm border border-gray-200 mb-1 py-3 px-4 transition-all duration-200"
                >
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <span className="font-bold text-base text-title-medium flex-shrink-0">{idx + 1}.</span>
                    <span
                      className="text-title-medium font-bold truncate block max-w-[320px]"
                      title={step}
                      aria-label={step}
                    >
                      {step}
                    </span>
                  </div>
                  <div className="flex flex-row flex-wrap gap-4 items-center ml-4">
                    <span className="text-paragraphContent">
                      <span className="text-body-small font-bold">Difficulty:</span> <span className="text-label-medium">Easy</span>
                    </span>
                    <span className="text-paragraphContent">
                      <span className="text-body-small font-bold">Duration:</span> <span className="text-label-medium">10 Minutes</span>
                    </span>
                    <span className="text-paragraphContent">
                      <span className="text-body-small font-bold">Reward:</span> <span className="text-label-medium">30 Points</span>
                    </span>
                    <Button variant="netural" size="sm" className="h-8 px-5 text-lightBlue" >
                      <span className="text-label-large">View</span>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Challenge Card */}
          <div className="bg-white rounded border border-gray-200 shadow p-4 flex flex-col h-max w-full max-w-sm mx-auto lg:max-w-[370px]">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-xl font-bold text-gray-900">{challenge.title}</h2>
              <button className="p-1 rounded hover:bg-gray-100">
                <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="10" cy="10" r="1" /><circle cx="16" cy="10" r="1" /><circle cx="4" cy="10" r="1" /></svg>
              </button>
            </div>
            <div className="text-xs text-gray-600 mb-1 flex items-center gap-1">
              <span className="font-bold">Areas of Life:</span> <div className="flex items-center gap-1">{challenge.areas.map((area: string, i: number) => <div key={i} className="inline-block w-6 h-6 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center text-xs font-bold border border-white -ml-2 first:ml-0"><AreaIcon key={i} area={area} /></div>)}</div>
            </div>
            <div className="relative w-full h-48 overflow-hidden mb-3">
              <Image src={challenge.image} alt={challenge.title} fill className="object-cover" />
            </div>
            <div className="flex flex-wrap items-center text-xs text-gray-700 mb-2 gap-2">
              <span> <span className="font-bold">Steps:</span> {challenge.steps}</span>
              <span> <span className="font-bold">Duration:</span> {challenge.duration}</span>
              <span> <span className="font-bold">Reward:</span> {challenge.reward} Points</span>
            </div>
            <div className="flex items-center gap-1 mb-3">
              {Array.from({ length: challenge.participants }).map((_, i) => (
                <span key={i} className="inline-block w-6 h-6 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center text-xs font-bold border border-white -ml-2 first:ml-0"><ChallengeUserIcon /></span>
              ))}
              <span className="inline-block w-6 h-6 rounded-full bg-gray-100 text-gray-500 flex items-center justify-center text-lg font-bold border border-white -ml-2">+</span>
            </div>
            <div className="mb-3">
              <div className="font-semibold text-sm mb-1">About the Challenge:</div>
              <div className="text-body-small text-paragraphContent leading-relaxed">
                {challenge.about}
              </div>
            </div>
            <div>
              <div className="font-semibold text-sm mb-1">About the Instructor:</div>
              <div className="flex items-center gap-2 mb-1">
                <span className="font-semibold text-xs text-gray-900">{challenge.instructor.name}</span>
                <span className="text-xs text-gray-500">Courses: {challenge.instructor.courses}</span>
              </div>
              <div className="text-body-small text-paragraphContent leading-relaxed">
                {challenge.instructor.bio}
              </div>
            </div>
          </div>
        </div>
      </div>
  );
} 