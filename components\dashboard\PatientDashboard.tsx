import React, { useState, useEffect } from 'react';
import { Tab } from './Tab';
import { HealthStats } from './patient/HealthStats';
import { Overview } from './patient/Overview';

export function PatientDashboard() {
  const [activeTab, setActiveTab] = useState<'overview' | 'program' | 'challenge' | 'health'>('overview');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => setIsMobile(window.innerWidth < 768);
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  let tabContent;
  if (activeTab === 'overview') {
    tabContent = (
      <Overview />
    );
  } else if (activeTab === 'program') {
    tabContent = (
      <div>
        {/* TODO: Implement Program Stats widgets here */}
        <div className="text-center text-gray-400 py-20">Program Stats content goes here.</div>
      </div>
    );
  } else if (activeTab === 'challenge') {
    tabContent = (
      <div>
        {/* TODO: Implement Challenge Stats widgets here */}
        <div className="text-center text-gray-400 py-20">Challenge Stats content goes here.</div>
      </div>
    );
  } else if (activeTab === 'health') {
    tabContent = (
      <HealthStats />
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <p className="text-3xl md:text-display-md text-paragraphContent">My Dashboard</p>
      </div>
      {/* Tabs */}
      <div className="border-b mb-2 md:mb-4 overflow-x-auto w-full">
        <div className="flex space-x-1 md:space-x-2 w-full">
          <Tab
            label="Overview"
            active={activeTab === 'overview'}
            onClick={() => setActiveTab('overview')}
          />
          <Tab
            label={isMobile ? 'Program' : 'Program Stats'}
            active={activeTab === 'program'}
            onClick={() => setActiveTab('program')}
          />
          <Tab
            label={isMobile ? 'Challenge' : 'Challenge Stats'}
            active={activeTab === 'challenge'}
            onClick={() => setActiveTab('challenge')}
          />
          <Tab
            label={isMobile ? 'Health' : 'Health Stats'}
            active={activeTab === 'health'}
            onClick={() => setActiveTab('health')}
          />
        </div>
      </div>
      {tabContent}
    </div>
  );
} 
