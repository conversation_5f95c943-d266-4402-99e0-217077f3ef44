"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer
} from "recharts"

interface LegendItem {
  label: string
  color: string
}

interface CircularProgressChartProps {
  title: string
  percentage: number
  color: string
  linkText: string
  linkHref: string
  legend: LegendItem[]
}

export function CircularProgressChart({
  title,
  percentage,
  color,
  linkText,
  linkHref,
  legend,
}: CircularProgressChartProps) {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Initial check
    checkIfMobile()

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile)

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  const data = [
    { name: "Active", value: percentage },
    { name: "Inactive", value: 100 - percentage }
  ]

  return (
    <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
      <h3 className="text-oldN30 text-body-lg font-bold mb-2 max-md:text-center">{title}</h3>
      <div className="flex items-center mb-3">
        {legend.map((item, index) => (
          <div key={index} className="flex items-center mr-4 max-md:mx-auto">
            <div
              className="w-3 h-3 rounded-full mr-2"
              style={{ backgroundColor: item.color }}
            ></div>
            <span className="text-body-large text-oldN30">{item.label}</span>
          </div>
        ))}
      </div>
      <div className="flex justify-center items-center h-[240px] w-[240px] relative mx-auto">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            {/* Background arc (full circle, light color) */}
            <Pie
              data={[{ value: 100 }]}
              cx="50%"
              cy="50%"
              innerRadius={90}
              outerRadius={110}
              startAngle={225}
              endAngle={-45}
              dataKey="value"
              strokeWidth={0}
              cornerRadius={20}
              isAnimationActive={false}
            >
              <Cell fill="#E5E7EB" />
            </Pie>
            {/* Foreground arc (progress, colored) */}
            <Pie
              data={[{ value: percentage }]}
              cx="50%"
              cy="50%"
              innerRadius={90}
              outerRadius={110}
              startAngle={225}
              endAngle={225 - (270 * (percentage / 100))}
              dataKey="value"
              strokeWidth={0}
              cornerRadius={20}
              isAnimationActive={true}
            >
              <Cell fill={color} />
            </Pie>
          </PieChart>
        </ResponsiveContainer>
        <div className="absolute left-1/2 bottom-0 transform -translate-x-1/2 -translate-y-1/2 flex flex-col items-center">
          <span className="text-[2.5rem] font-bold text-gray-500">{percentage}%</span>
        </div>
      </div>
      <div className="text-center mt-4">
        <Link
          href={linkHref}
          className="inline-block px-5 py-1 text-darkBlueNormal text-title-medium border border-darkBlueNormal rounded-lg hover:bg-gray-50 transition"
        >
          {linkText}
        </Link>
      </div>
    </div>
  )
}
