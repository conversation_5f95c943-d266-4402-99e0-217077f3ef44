"use client"

import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

interface ProgramsHeaderProps {
  title: string
}

export function ProgramsHeader({ title }: ProgramsHeaderProps) {
  const router = useRouter();
  const userRole = useSelector((state: RootState) => state.user.user?.role);

  return (
    <div className="border-b flex justify-between items-center pb-6">
      <p className="text-3xl font-bold md:text-display-md text-paragraphContent">{title}</p>
      {userRole === 'provider' && (
        <Button variant="outlinedDark" size="sm" onClick={() => router.push('/dashboard/programs/new')}>
          <Plus className="w-4 h-4" /> <span className="hidden sm:block">New Program</span>
        </Button>
      )}
    </div>
  )
}
