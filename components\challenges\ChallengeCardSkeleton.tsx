import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

export function ChallengeCardSkeleton() {
  return (
    <div className="bg-white rounded shadow border p-4 flex flex-col w-[360px]">
      <div className="flex items-center justify-between mb-2">
        <Skeleton width={180} height={24} />
        <Skeleton width={24} height={24} circle />
      </div>
      <div className="text-xs text-gray-600 mb-1 flex items-center gap-1">
        <span className="text-body-small text-paragraphContent">Areas of Life:</span>
        <div className="flex items-center">
          {Array.from({ length: 9 }).map((_, i) => (
            <Skeleton key={i} width={24} height={24} circle />
          ))}
        </div>
      </div>
      <div className="relative w-full h-48 overflow-hidden mb-3">
        <Skeleton height={192} />
      </div>
      <div className="flex flex-wrap items-center text-xs text-gray-700 mb-2 gap-2">
        <Skeleton width={60} height={16} />
        <Skeleton width={80} height={16} />
        <Skeleton width={90} height={16} />
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} width={24} height={24} circle />
          ))}
          <Skeleton width={24} height={24} circle />
        </div>
        <Skeleton width={80} height={32} borderRadius={8} />
      </div>
    </div>
  );
} 