import Image from "next/image";
import { Button } from "@/components/ui/button";
import { AreaIcon } from "@/components/dashboard/AreaIcon";
import { ChallengeUserIcon } from "@/utils/icons";

interface ChallengeCardProps {
  title: string;
  image: string;
  areas: string[];
  steps: number;
  duration: string;
  reward: number;
  participants: number;
  onDetailsClick?: () => void;
}

export function ChallengeCard({
  title,
  image,
  areas,
  steps,
  duration,
  reward,
  participants,
  onDetailsClick,
}: ChallengeCardProps) {
  return (
    <div className="bg-white rounded shadow border p-4 flex flex-col w-[350px] max-md:w-full">
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-title-medium text-paragraphContent font-bold">{title}</h2>
        <button className="p-1 rounded hover:bg-gray-100">
          <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="10" cy="10" r="1" /><circle cx="16" cy="10" r="1" /><circle cx="4" cy="10" r="1" /></svg>
        </button>
      </div>
      <div className="text-xs text-gray-600 mb-1 flex items-center gap-1">
        <span className="text-body-small text-paragraphContent">Areas of Life:</span> <div className="flex items-center gap-1">{areas.map((area, i) => <div key={i} className="inline-block w-6 h-6 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center text-xs font-bold border border-white -ml-2 first:ml-0"><AreaIcon key={i} area={area} /></div>)   }</div>
      </div>
      <div className="w-full overflow-hidden my-2">
        <Image src={image} alt={title} width={328} height={200} className="object-cover max-md:w-full" />
      </div>
      <div className="flex flex-wrap items-center text-body-small text-paragraphContent mb-2 gap-2">
        <span> <span className="font-bold">Steps:</span> {steps}</span>
        <span> <span className="font-bold">Duration:</span> {duration}</span>
        <span> <span className="font-bold">Reward:</span> {reward} Points</span>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          {Array.from({ length: participants }).map((_, i) => (
            <span key={i} className="inline-block w-6 h-6 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center text-xs font-bold border border-white -ml-2 first:ml-0"><ChallengeUserIcon /></span>
          ))}
          <span className="inline-block w-6 h-6 rounded-full bg-gray-100 text-gray-500 flex items-center justify-center text-lg font-bold border border-white -ml-2">+</span>
        </div>
        <Button variant="netural" size="sm" className="h-8 text-lightBlue" onClick={onDetailsClick}>
          <span className="text-body-medium">Details</span>
        </Button>
      </div>
    </div>
  );
} 