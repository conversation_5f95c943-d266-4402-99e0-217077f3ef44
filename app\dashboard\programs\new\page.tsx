"use client";

import { useState } from "react";
import ProgramDetailsStep from "@/components/programs/ProgramDetailsStep";
import ProgramChallengeStep from "@/components/programs/ProgramChallengesStep";
import ProgramPreviewStep from "@/components/programs/ProgramPreviewStep";
import { PatientPrevBtn, ProgressBtn, ProgressCheck, GreenCheck, RedCheck, LightRedCheck, BrownCheck, BlueCheck, DarkBlueCheck, YellowCheck, PurpleCheck, DarkGreenCheck } from "@/utils/icons";
import { CHALLENGES } from "@/lib/challenges_mockup";
import Link from "next/link";

export default function NewProgramPage() {
  const [step, setStep] = useState(0); // Start with step 0 for ProgramDetailsStep
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [instructor, setInstructor] = useState("");
  const [areas, setAreas] = useState<string[]>([]);
  const [media, setMedia] = useState<string>("");

  // Areas of Life icons state - shared across all components
  const [areasOfLifeIcons, setAreasOfLifeIcons] = useState([
    <GreenCheck key="green" />,
    <RedCheck key="red" />,
    <LightRedCheck key="lightred" />,
    <BrownCheck key="brown" />,
    <BlueCheck key="blue" />,
    <DarkBlueCheck key="darkblue" />,
    <YellowCheck key="yellow" />,
    <PurpleCheck key="purple" />,
    <DarkGreenCheck key="darkgreen" />
  ]);

  // Challenges state
  const [challenges, setChallenges] = useState(CHALLENGES);
  const [lockOrder, setLockOrder] = useState(false); // Start with false so users can select cards

  const handleMediaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setMedia(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveMedia = () => {
    setMedia("");
  };

  const handleSave = () => {
    // TODO: Add your API call here to save the program
    // Redirect to programs page after saving
    window.location.href = '/dashboard/programs';
  };



  return (
    <div className="flex flex-col min-h-screen">
      <div>
        <p className="text-display-md text-paragraphContent">New Program</p>
        <Link
          href="/dashboard/programs"
          className="flex items-center mt-6 pb-3 mb-3 border-b border-gray-200"
        >
          <PatientPrevBtn />
          <span className="ml-2 text-title-medium text-contentColor">
            Back to All Programs
          </span>
        </Link>
      </div>
      <div className="w-full bg-white shadow-sm border border-gray-200 rounded-lg max-w-[1042px]">
        {/* Progress Bar - Full Width Like First Image */}
        <div className="flex w-full">
          {/* Step 1 */}
          <div className={`flex items-center justify-center gap-2 sm:gap-3 flex-1 py-4 border-t-4 transition-all duration-200 ${step >= 0 ? "border-darkerBlue bg-lightBlue" : "border-gray-300 bg-gray-50"
            }`}>
            <div className="flex items-center justify-center">
              {step > 0 ? (
                <ProgressCheck />
              ) : step === 0 ? (
                <ProgressBtn />
              ) : (
                <div className="w-6 h-6 rounded-full border-2 border-gray-300 bg-white"></div>
              )}
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700 text-center">
              1. Fill out Program Details
            </span>
          </div>

          {/* Step 2 */}
          <div className={`flex items-center justify-center gap-2 sm:gap-3 flex-1 py-4 border-t-4 transition-all duration-200 ${step >= 1 ? "border-darkerBlue bg-lightBlue" : "border-gray-300 bg-gray-50"
            }`}>
            <div className="flex items-center justify-center">
              {step > 1 ? (
                <ProgressCheck />
              ) : step === 1 ? (
                <ProgressBtn />
              ) : (
                <div className="w-6 h-6 rounded-full border-2 border-gray-300 bg-white"></div>
              )}
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700 text-center">
              2. Add Challenges
            </span>
          </div>

          {/* Step 3 */}
          <div className={`flex items-center justify-center gap-2 sm:gap-3 flex-1 py-4 border-t-4 transition-all duration-200 ${step >= 2 ? "border-darkerBlue bg-lightBlue" : "border-gray-300 bg-gray-50"
            }`}>
            <div className="flex items-center justify-center">
              {step > 2 ? (
                <ProgressCheck />
              ) : step === 2 ? (
                <ProgressBtn />
              ) : (
                <div className="w-6 h-6 rounded-full border-2 border-gray-300 bg-white"></div>
              )}
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700 text-center">
              3. Preview Program
            </span>
          </div>
        </div>
      </div>

      {/* Content Area - Fully Responsive */}
      <div className="w-full bg-lightBlue min-h-[calc(100vh-200px)] sm:min-h-[calc(100vh-250px)] lg:min-h-[calc(100vh-300px)] max-w-[1042px] rounded-lg">
        {step === 0 && (
          <ProgramDetailsStep
            name={name}
            setName={setName}
            description={description}
            setDescription={setDescription}
            media={media}
            setMedia={setMedia}
            handleMediaChange={handleMediaChange}
            handleRemoveMedia={handleRemoveMedia}
            areasOfLifeIcons={areasOfLifeIcons}
            onNext={() => {
              console.log('Moving to step 1');
              setStep(1);
            }}
          />
        )}
        {step === 1 && (
          <ProgramChallengeStep
            challenges={challenges}
            setChallenges={setChallenges}
            lockOrder={lockOrder}
            setLockOrder={setLockOrder}
            areasOfLifeIcons={areasOfLifeIcons}
            onBack={() => {
              console.log('Moving back to step 0');
              setStep(0);
            }}
            onNext={() => {
              console.log('Moving to step 2');
              setStep(2);
            }}
          />
        )}
        {step === 2 && (
          <ProgramPreviewStep
            name={name}
            description={description}
            media={media}
            challenges={challenges}
            lockOrder={lockOrder}
            areasOfLifeIcons={areasOfLifeIcons}
            onBack={() => {
              console.log('Moving back to step 1');
              setStep(1);
            }}
            onSave={handleSave}
          />
        )}
      </div>
    </div>
  );
}