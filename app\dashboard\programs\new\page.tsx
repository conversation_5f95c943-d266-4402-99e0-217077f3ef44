"use client";

import { useState } from "react";
import ProgramDetailsStep from "@/components/dashboard/ProgramDetailsStep";
import ProgramChallengesStep from "@/components/dashboard/ProgramChallengesStep";
import ProgramPreviewStep from "@/components/dashboard/ProgramPreviewStep";
import { PatientPrevBtn, ProgressBtn, ProgressCheck } from "@/utils/icons";
import Link from "next/link";

const MOCK_CHALLENGES = [
  {
    id: "1",
    title: "Wheel of Life Challenge",
    steps: 30,
    duration: 30,
    reward: 300,
    selected: false,
  },
  {
    id: "2",
    title: "33 Questions Challenge",
    steps: 25,
    duration: 25,
    reward: 250,
    selected: false,
  },
  {
    id: "3",
    title: "Decision Destination",
    steps: 20,
    duration: 20,
    reward: 200,
    selected: false,
  },
  {
    id: "4",
    title: "Just in Case",
    steps: 15,
    duration: 15,
    reward: 150,
    selected: false,
  },
  {
    id: "5",
    title: "Pulling it Together",
    steps: 10,
    duration: 10,
    reward: 100,
    selected: false,
  },
  {
    id: "6",
    title: "The Toolkit",
    steps: 35,
    duration: 35,
    reward: 350,
    selected: false,
  },
];

export default function NewProgramPage() {
  const [step, setStep] = useState(0); // Start with step 0 for ProgramDetailsStep

  // Program details state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [media, setMedia] = useState<string>("");

  // Challenges state
  const [challenges, setChallenges] = useState(MOCK_CHALLENGES);
  const [lockOrder, setLockOrder] = useState(true);

  const handleMediaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setMedia(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveMedia = () => {
    setMedia("");
  };

  const handleSave = () => {
    window.location.href = '/dashboard/programs';
  };



  return (
    <div className="flex flex-col min-h-screen">
      <div>
        <p className="text-display-md text-paragraphContent">New Program</p>
        <Link
          href="/dashboard/programs"
          className="flex items-center mt-6 pb-3 mb-3 border-b border-gray-200"
        >
          <PatientPrevBtn />
          <span className="ml-2 text-title-medium text-contentColor">
            Back to All Programs
          </span>
        </Link>
      </div>
      <div className="full bg-white shadow max-w-[1042px]">
        {/* Progress Bar */}
        <div className="flex flex-col sm:flex-row items-center">
          {/* Step 1 */}
          <div
            className={`flex flex-col items-center gap-2 w-full sm:flex-1 py-4 border-t-[4px] bg-landingBackground  ${
              step > 0 ? "border-darkerBlue" : "border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              {step > 0 ? (
                <ProgressCheck />
              ) : (
                <ProgressBtn />
              )}
            </div>
            <span className="text-xs sm:text-sm text-center px-2">
              1. Fill out Program Details
            </span>
          </div>
          {/* Step 2 */}
          <div
            className={`flex flex-col items-center gap-2 w-full sm:flex-1 py-4 border-t-[4px] bg-landingBackground  ${
              step > 1 ? "border-darkerBlue" : "border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              {step > 1 ? (
                <ProgressCheck />
              ) : (
                <ProgressBtn />
              )}
            </div>
            <span className="text-xs sm:text-sm text-center px-2">
              2. Add Challenges
            </span>
          </div>
          {/* Step 3 */}
          <div
            className={`flex flex-col items-center gap-2 w-full sm:flex-1 py-4 border-t-[4px] bg-landingBackground  ${
              step > 2 ? "border-darkerBlue" : "border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              {step > 2 ? (
                <ProgressCheck />
              ) : (
                <ProgressBtn />
              )}
            </div>
            <span className="text-xs sm:text-sm text-center px-2">
              3. Preview Program
            </span>
          </div>
        </div>
      </div>
      <div className="w-full bg-lightBlue min-h-[calc(100vh-400px)]  max-w-[1042px]">
        {step === 0 && (
          <ProgramDetailsStep
            name={name}
            setName={setName}
            description={description}
            setDescription={setDescription}
            media={media}
            setMedia={setMedia}
            handleMediaChange={handleMediaChange}
            handleRemoveMedia={handleRemoveMedia}
            onNext={() => setStep(1)}
          />
        )}
        {step === 1 && (
          <ProgramChallengesStep
            challenges={challenges}
            setChallenges={setChallenges}
            lockOrder={lockOrder}
            setLockOrder={setLockOrder}
            onBack={() => setStep(0)}
            onNext={() => setStep(2)}
          />
        )}
        {step === 2 && (
          <ProgramPreviewStep
            name={name}
            description={description}
            media={media}
            challenges={challenges}
            lockOrder={lockOrder}
            onBack={() => setStep(1)}
            onSave={handleSave}
          />
        )}
      </div>
    </div>
  );
}