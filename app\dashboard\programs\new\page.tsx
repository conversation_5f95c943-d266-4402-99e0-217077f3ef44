"use client";

import { useState } from "react";
import ProgramDetailsStep from "@/components/programs/ProgramDetailsStep";
import ProgramChallengeStep from "@/components/programs/ProgramChallengesStep";
import ProgramPreviewStep from "@/components/programs/ProgramPreviewStep";
import { PatientPrevBtn, ProgressBtn, ProgressCheck } from "@/utils/icons";
import { Circle } from "lucide-react";
import Link from "next/link";

const MOCK_CHALLENGES = [
  {
    id: "1",
    title: "Wheel of Life Challenge",
    steps: 30,
    duration: 30,
    reward: 300,
    selected: false,
  },
  {
    id: "2",
    title: "33 Questions Challenge",
    steps: 25,
    duration: 25,
    reward: 250,
    selected: false,
  },
  {
    id: "3",
    title: "Decision Destination",
    steps: 20,
    duration: 20,
    reward: 200,
    selected: false,
  },
  {
    id: "4",
    title: "Just in Case",
    steps: 15,
    duration: 15,
    reward: 150,
    selected: false,
  },
  {
    id: "5",
    title: "Pulling it Together",
    steps: 10,
    duration: 10,
    reward: 100,
    selected: false,
  },
  {
    id: "6",
    title: "The Toolkit",
    steps: 35,
    duration: 35,
    reward: 350,
    selected: false,
  },
];

export default function NewProgramPage() {
  const [step, setStep] = useState(0); // Start with step 0 for ProgramDetailsStep

  // Program details state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [instructor, setInstructor] = useState("");
  const [areas, setAreas] = useState<string[]>([]);
  const [media, setMedia] = useState<string>("");

  // Challenges state
  const [challenges, setChallenges] = useState(MOCK_CHALLENGES);
  const [lockOrder, setLockOrder] = useState(true);

  // Handler functions
  const handleAreaToggle = (area: string) => {
    setAreas(prev =>
      prev.includes(area)
        ? prev.filter(a => a !== area)
        : [...prev, area]
    );
  };

  const handleMediaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setMedia(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveMedia = () => {
    setMedia("");
  };

  const handleSave = () => {
    // Log the complete program data
    console.log({
      name,
      description,
      instructor,
      areas,
      media,
      challenges: challenges.filter(c => c.selected),
      lockOrder,
    });

    // TODO: Add your API call here to save the program
    // Redirect to programs page after saving
    window.location.href = '/dashboard/programs';
  };



  return (
    <div className="flex flex-col min-h-screen">
      <div>
        <p className="text-display-md text-paragraphContent">New Program</p>
        <Link
          href="/dashboard/programs"
          className="flex items-center mt-6 pb-3 mb-3 border-b border-gray-200"
        >
          <PatientPrevBtn />
          <span className="ml-2 text-title-medium text-contentColor">
            Back to All Programs
          </span>
        </Link>
      </div>
      <div className="w-full bg-white shadow-sm border border-gray-200 rounded-lg max-w-[1042px]">
        {/* Progress Bar - Reduced Height Like Image */}
        <div className="flex items-center px-4 py-3 bg-gray-50 rounded-lg">
          {/* Step 1 */}
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="flex items-center justify-center">
              {step > 0 ? (
                <ProgressCheck />
              ) : step === 0 ? (
                <ProgressBtn />
              ) : (
                <div className="w-6 h-6 rounded-full border-2 border-gray-300 bg-white"></div>
              )}
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">
              1. Fill out Program Details
            </span>
          </div>

          {/* Step 2 */}
          <div className="flex items-center gap-2 sm:gap-3 ml-6 sm:ml-8">
            <div className="flex items-center justify-center">
              {step > 1 ? (
                <ProgressCheck />
              ) : step === 1 ? (
                <ProgressBtn />
              ) : (
                <div className="w-6 h-6 rounded-full border-2 border-gray-300 bg-white"></div>
              )}
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">
              2. Add Challenges
            </span>
          </div>

          {/* Step 3 */}
          <div className="flex items-center gap-2 sm:gap-3 ml-6 sm:ml-8">
            <div className="flex items-center justify-center">
              {step > 2 ? (
                <ProgressCheck />
              ) : step === 2 ? (
                <ProgressBtn />
              ) : (
                <div className="w-6 h-6 rounded-full border-2 border-gray-300 bg-white"></div>
              )}
            </div>
            <span className="text-xs sm:text-sm font-medium text-gray-700 whitespace-nowrap">
              3. Preview Program
            </span>
          </div>
        </div>
      </div>

      {/* Content Area - Fully Responsive */}
      <div className="w-full bg-lightBlue min-h-[calc(100vh-200px)] sm:min-h-[calc(100vh-250px)] lg:min-h-[calc(100vh-300px)] max-w-[1042px] rounded-lg">
        {step === 0 && (
          <ProgramDetailsStep
            name={name}
            setName={setName}
            description={description}
            setDescription={setDescription}
            instructor={instructor}
            setInstructor={setInstructor}
            areas={areas}
            setAreas={setAreas}
            media={media}
            setMedia={setMedia}
            handleAreaToggle={handleAreaToggle}
            handleMediaChange={handleMediaChange}
            handleRemoveMedia={handleRemoveMedia}
            onNext={() => {
              console.log('Moving to step 1');
              setStep(1);
            }}
          />
        )}
        {step === 1 && (
          <ProgramChallengeStep
            challenges={challenges}
            setChallenges={setChallenges}
            lockOrder={lockOrder}
            setLockOrder={setLockOrder}
            onBack={() => {
              console.log('Moving back to step 0');
              setStep(0);
            }}
            onNext={() => {
              console.log('Moving to step 2');
              setStep(2);
            }}
          />
        )}
        {step === 2 && (
          <ProgramPreviewStep
            name={name}
            description={description}
            instructor={instructor}
            areas={areas}
            media={media}
            challenges={challenges}
            lockOrder={lockOrder}
            onBack={() => {
              console.log('Moving back to step 1');
              setStep(1);
            }}
            onSave={handleSave}
          />
        )}
      </div>
    </div>
  );
}