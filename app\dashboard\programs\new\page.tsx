"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>L<PERSON><PERSON>, Check, Circle, ArrowUpFromLine } from "lucide-react";
import SecondStep from "./SecondStep";
import ThirdStep from './ThirdStep';
import { PreviewNewProgram } from "@/utils/icons";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
const ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif", "image/webp"];
const ALLOWED_VIDEO_TYPES = ["video/mp4", "video/webm", "video/ogg", "video/quicktime", "video/mov"];
const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES];

const MOCK_CHALLENGES = [
  {
    id: 1,
    title: "33 Questions Challenge",
    areasOfLife: ["#FF5252", "#FF7B7B", "#FF9E80", "#4FC3F7", "#2196F3", "#FFC107", "#FFEB3B", "#8BC34A", "#4CAF50", "#009688"],
    steps: 30,
    duration: 30,
    reward: 300,
  },
  {
    id: 2,
    title: "Decision Destination Challenge",
    areasOfLife: ["#FF5252", "#FF7B7B", "#FF9E80", "#4FC3F7", "#2196F3", "#FFC107", "#FFEB3B", "#8BC34A", "#4CAF50", "#009688"],
    steps: 30,
    duration: 30,
    reward: 300,
  },
  {
    id: 3,
    title: "Just in Case Challenge",
    areasOfLife: ["#FF5252", "#FF7B7B", "#FF9E80", "#4FC3F7", "#2196F3", "#FFC107", "#FFEB3B", "#8BC34A", "#4CAF50", "#009688"],
    steps: 30,
    duration: 30,
    reward: 300,
  },
  {
    id: 4,
    title: "Pulling it Together Challenge",
    areasOfLife: ["#FF5252", "#FF7B7B", "#FF9E80", "#4FC3F7", "#2196F3", "#FFC107", "#FFEB3B", "#8BC34A", "#4CAF50", "#009688"],
    steps: 30,
    duration: 30,
    reward: 300,
  }
];

export default function NewProgramPage() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [programName, setProgramName] = useState("");
  const [programDescription, setProgramDescription] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string>("");
  const [playVideo, setPlayVideo] = useState(false);
  const [challenges, setChallenges] = useState(MOCK_CHALLENGES);
  const [lockOrder, setLockOrder] = useState(true);
  const [dragIndex, setDragIndex] = useState<number | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError("");
    setPlayVideo(false);
    if (e.target.files && e.target.files[0]) {
      const selected = e.target.files[0];
      if (!ALLOWED_TYPES.includes(selected.type)) {
        setError("Only image and video files (jpg, png, gif, mp4, webm, mov, etc.) are allowed.");
        setFile(null);
        return;
      }
      setFile(selected);
    }
  };

  const handlePlayVideo = (e: React.MouseEvent) => {
    e.preventDefault();
    setPlayVideo(true);
  };

  const handleRemove = () => {
    setFile(null);
    setPlayVideo(false);
    setError("");
  };

  const renderPreview = () => {
    if (!file) return (
      <PreviewNewProgram />
    );
    if (file.type.startsWith('image/')) {
      return <img src={URL.createObjectURL(file)} alt="Preview" className="w-full h-full rounded max-md:mb-2" />;
    }
    if (file.type.startsWith('video/')) {
      if (!playVideo) {
        return (
          <div className="relative w-full h-full flex items-center justify-center cursor-pointer" onClick={handlePlayVideo}>
            <video
              src={URL.createObjectURL(file)}
              className="w-full h-full object-cover rounded opacity-70"
              preload="metadata"
              poster=""
              onLoadedMetadata={e => (e.currentTarget.currentTime = 0)}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="24" cy="24" r="24" fill="#222c37" fillOpacity="0.7" />
                <polygon points="20,16 36,24 20,32" fill="#fff" />
              </svg>
            </div>
          </div>
        );
      } else {
        return (
          <video
            src={URL.createObjectURL(file)}
            controls
            autoPlay
            className="w-full h-full object-cover rounded"
          />
        );
      }
    }
    return null;
  };

  // Drag and drop handlers for challenges
  const handleDragStart = (index: number) => {
    setDragIndex(index);
  };
  const handleDragOver = (index: number) => {
    if (dragIndex === null || dragIndex === index) return;
    const updated = [...challenges];
    const [removed] = updated.splice(dragIndex, 1);
    updated.splice(index, 0, removed);
    setChallenges(updated);
    setDragIndex(index);
  };
  const handleDragEnd = () => {
    setDragIndex(null);
  };

  // Stepper rendering (tab style)
  const renderStepper = () => (
    <div className="flex items-end pb-0 gap-0 w-full overflow-x-auto flex-nowrap rounded-lg">
      {[1, 2, 3].map((s, i) => {
        const isActive = step === s;
        const isCompleted = step > s;
        return (
          <div
            key={s}
            className={`flex-1 min-w-[200px] sm:min-w-[220px] text-body-medium flex flex-col items-center relative group transition-all duration-200 py-1
              ${isActive ? 'bg-white border-t-4 border-darkerBlue' : 
                isCompleted ? 'bg-landingBackground border-t-4 border-darkerBlue' : 
                'bg-transparent border-t-4 border-mediumGray'}
              ${isActive || isCompleted ? 'z-10' : 'z-0'}`}
            style={{ minWidth: 0 }}
          >
            <div className="flex items-center w-full py-1 sm:py-2 px-1 sm:px-2 text-xs sm:text-title-small">
              {isCompleted ? (
                <Check className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-green-500" />
              ) : isActive ? (
                <Circle className="w-3 h-3 sm:w-4 sm:h-4 text-paragraphContent mr-1 sm:mr-2" />
              ) : (
                <Circle className="w-3 h-3 sm:w-4 sm:h-4 text-mediumGray mr-1 sm:mr-2" />
              )}
              <span className={`truncate ${isCompleted || isActive ? 'text-paragraphContent' : 'text-mediumGray'}`}>
                {s === 1 ? '1. Fill out Program Details' : s === 2 ? '2. Add Challenges' : '3. Preview Program'}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );

  return (
    <div className="min-h-screen flex flex-col items-center">
      <div className="w-full flex flex-col items-start mb-4">
        <h1 className="text-3xl md:text-display-md text-paragraphContent mb-6">New Program</h1>
        <div className="w-full border-b border-gray-200 pb-3">
          <button
            className="flex items-center text-title-medium text-paragraphContent mt-2 hover:underline"
            onClick={() => router.push("/dashboard/programs")}
            type="button"
          >
            <ArrowLeft size={16} className="mr-3" />
            Back to All Programs
          </button>
        </div>
      </div>
      <div className="flex flex-col w-full items-start">
        <div className="bg-white w-full max-w-full sm:max-w-[800px] rounded-sm border border-gray-200 shadow-sm p-0">
          {renderStepper()}
          {step === 1 && (
            <form
              onSubmit={e => { e.preventDefault(); setStep(2); }}
              className="mt-5 px-2 sm:px-4 md:px-8 pb-8 pt-2 text-left mx-auto w-full sm:max-w-[530px]"
            >
              <div className="text-lg sm:text-xl font-bold text-paragraphContent mb-4 border-b border-gray-200 pb-4">
                Fill out Program Details:
              </div>
              <div className="mt-7">
                <label className="block text-sm sm:text-base font-bold text-gray-800 mb-2">Program Name</label>
                <Input
                  type="text"
                  className="w-full bg-landingBackground border-0 border-b-2 px-3 py-2 text-sm sm:text-base"
                  value={programName}
                  onChange={e => setProgramName(e.target.value)}
                  required
                />
              </div>
              <div className="mt-7">
                <label className="block text-sm sm:text-base font-bold text-gray-800 mb-2">Program Description</label>
                <textarea
                  className="w-full bg-landingBackground border-0 border-b-2 px-3 py-2 text-sm sm:text-base"
                  rows={4}
                  value={programDescription}
                  onChange={e => setProgramDescription(e.target.value)}
                  required
                />
              </div>
              <div className="flex flex-col mt-7 mb-2">
                <label className="text-sm sm:text-base font-bold text-gray-800 mb-2 w-full text-left">Choose a Photo or Video</label>
                <div className="flex max-md:flex-col w-full">
                  <div className="flex flex-col items-center">
                    <div className="w-[130px] sm:h-28 flex justify-center items-center mt-2 mb-2">{renderPreview()}</div>
                    {error && <div className="text-xs text-red-500 mb-2">{error}</div>}
                  </div>
                  <div className="flex items-center gap-2 w-full">
                    <input
                      type="file"
                      accept="image/*,video/*"
                      className="hidden"
                      id="file-upload"
                      onChange={handleFileChange}
                    />
                    <div className="flex flex-col mx-auto">
                      <label htmlFor="file-upload" className="flex items-center border border-darkBlueNormal rounded px-4 py-1 text-sm font-semibold text-darkBlueNormal bg-white cursor-pointer hover:bg-gray-50">
                        <ArrowUpFromLine className="w-4 h-4 mr-2" />
                        Upload
                      </label>
                      <button
                        type="button"
                        className={`text-xs rounded px-3 py-1 ml-2 mt-1 font-semibold ${file ? 'text-gray-700 bg-gray-100 cursor-pointer' : 'text-gray-400 cursor-not-allowed'}`}
                        onClick={handleRemove}
                        disabled={!file}
                      >
                        remove
                      </button>
                    </div>
                  </div>
                  <div className="text-xs text-paragraphContent max-w-[140px] w-full flex flex-col justify-center max-md:text-center max-md:mx-auto max-md:mt-5">
                    <div className="font-bold mb-1">Image requirements:</div>
                    <div>1. Min. 400 x 400px</div>
                    <div>2. Max. 2MB</div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row justify-end gap-4 sm:gap-0 border-t border-gray-200 pt-8">
                <Button
                  type="submit"
                  variant="netural"
                  className="h-8 rounded-lg"
                >
                  Next
                </Button>
              </div>
            </form>
          )}
          {step === 2 && (
            <SecondStep
              challenges={challenges}
              lockOrder={lockOrder}
              setLockOrder={setLockOrder}
              handleDragStart={handleDragStart}
              handleDragOver={handleDragOver}
              handleDragEnd={handleDragEnd}
              setStep={setStep}
            />
          )}
          {step === 3 && (
            <ThirdStep
              programName={programName}
              file={file}
              challenges={challenges}
              setStep={setStep}
            />
          )}
        </div>
      </div>
    </div>
  );
} 