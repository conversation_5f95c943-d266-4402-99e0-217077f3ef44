import React, { useState, useMemo } from 'react';
import {
  Bar<PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { PolarArea } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
  ArcElement,
  Tooltip as ChartTooltip,
  Legend as ChartLegend,
  Plugin
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { GreenCheck, RedCheck, BlueCheck, DarkBlueCheck, YellowCheck, PurpleCheck, DarkGreenCheck } from '@/utils/icons';
import { DonutChart } from "@/components/dashboard/DonutChart";
import { Heart, Zap, Flag } from 'lucide-react';
// Register Chart.js components
ChartJS.register(RadialLinearScale, ArcElement, ChartTooltip, ChartLegend, ChartDataLabels);

// Custom plugin for curved label lines and always visible numbers with hover effects
const polarAreaLabelLines: Plugin = {
  id: 'polarAreaLabelLines',
  afterDraw(chart: any) {
    const { ctx, data } = chart;
    const meta = chart.getDatasetMeta(0);

    // Store the active segment index
    let activeIndex = -1;
    if (chart.tooltip && chart.tooltip._active && chart.tooltip._active.length) {
      activeIndex = chart.tooltip._active[0].index;
    }

    meta.data.forEach((arc: any, i: number) => {
      const model = arc.getProps(['startAngle', 'endAngle', 'outerRadius', 'x', 'y'], true);
      const angle = (model.startAngle + model.endAngle) / 2;

      // Check if this segment is active (being hovered)
      const isActive = i === activeIndex;

      // Start at edge of segment
      const r1 = model.outerRadius - (isActive ? 2 : 4);
      // Curve control point (a bit outside the segment)
      const r2 = model.outerRadius + (isActive ? 22 : 18);
      // End point for number (further out)
      const r3 = model.outerRadius + (isActive ? 38 : 32);

      const x1 = model.x + Math.cos(angle) * r1;
      const y1 = model.y + Math.sin(angle) * r1;
      const cx = model.x + Math.cos(angle) * r2;
      const cy = model.y + Math.sin(angle) * r2;
      const x2 = model.x + Math.cos(angle) * r3;
      const y2 = model.y + Math.sin(angle) * r3;

      ctx.save();
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.quadraticCurveTo(cx, cy, x2, y2);

      // Get color from dataset
      let color = '#222';
      if (Array.isArray(data.datasets[0].backgroundColor)) {
        color = data.datasets[0].backgroundColor[i] as string || '#222';
      } else if (typeof data.datasets[0].backgroundColor === 'string') {
        color = data.datasets[0].backgroundColor;
      }

      // Apply different styling for active segment
      ctx.strokeStyle = isActive ? color : color;
      ctx.lineWidth = isActive ? 2 : 1.2;
      ctx.stroke();

      // Draw value with different style for active segment
      ctx.font = isActive ? 'bold 20px Inter, Arial' : 'bold 18px Inter, Arial';
      ctx.fillStyle = isActive ? color : color;
      ctx.textAlign = x2 > model.x ? 'left' : 'right';
      ctx.textBaseline = 'middle';

      let value = '';
      if (Array.isArray(data.datasets[0].data)) {
        value = String(data.datasets[0].data[i]);
      } else {
        value = String(data.datasets[0].data);
      }

      // Add "/10" for active segment
      const displayValue = isActive ? `${value}/10` : value;
      ctx.fillText(displayValue, x2 + (x2 > model.x ? 8 : -8), y2);

      // Draw category name for active segment
      if (isActive && data.labels && data.labels[i]) {
        ctx.font = '12px Inter, Arial';
        ctx.fillStyle = '#666';
        ctx.fillText(data.labels[i], x2 + (x2 > model.x ? 8 : -8), y2 + 20);
      }

      ctx.restore();
    });
  }
};
ChartJS.register(polarAreaLabelLines);

export function Overview() {
  // Wheel of Life data - memoized to prevent recreation on each render
  const wheelOfLifeData = useMemo(() => [
    { name: 'Finance', value: 6, color: '#8BBB4B' },
    { name: 'Romance', value: 5, color: '#CC1F70' },
    { name: 'Family', value: 5, color: '#EE5D60' },
    { name: 'Creative', value: 2, color: '#814679' },
    { name: 'Physical', value: 6, color: '#324D88' },
    { name: 'Social', value: 7, color: '#2472AB' },
    { name: 'Career', value: 7, color: '#2BACA4' },
    { name: 'Emotions', value: 4, color: '#34847D' },
    { name: 'Spiritual', value: 4, color: '#338F5E' },
  ], []);

  // Daily Health Stats
  const healthStats = useMemo(() => ({
    heartRate: 78,
    sleep: '6/8',
    steps: '5,000'
  }), []);

  // Calendar data
  const daysOfWeek = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
  // Calendar navigation state
  const todayDate = new Date();
  const [calendarMonth, setCalendarMonth] = useState(todayDate.getMonth());
  const [calendarYear, setCalendarYear] = useState(todayDate.getFullYear());

  function handlePrevMonth() {
    setCalendarMonth((prev) => {
      if (prev === 0) {
        setCalendarYear((y) => y - 1);
        return 11;
      }
      return prev - 1;
    });
  }
  function handleNextMonth() {
    setCalendarMonth((prev) => {
      if (prev === 11) {
        setCalendarYear((y) => y + 1);
        return 0;
      }
      return prev + 1;
    });
  }

  // Helper to get days in month
  function getDaysInMonth(month: number, year: number): number {
    return new Date(year, month + 1, 0).getDate();
  }
  // Helper to get first day of week (0=Sun)
  function getFirstDayOfWeek(month: number, year: number): number {
    return new Date(year, month, 1).getDay();
  }


  // Generate calendar days for current view - memoized based on month and year
  const calendarDays = useMemo(() => {
    const daysInMonth = getDaysInMonth(calendarMonth, calendarYear);
    const firstDayOfMonth = getFirstDayOfWeek(calendarMonth, calendarYear);
    const prevMonth = calendarMonth === 0 ? 11 : calendarMonth - 1;
    const prevMonthYear = calendarMonth === 0 ? calendarYear - 1 : calendarYear;
    const daysInPrevMonth = getDaysInMonth(prevMonth, prevMonthYear);

    const previousMonthDays = Array.from(
      { length: firstDayOfMonth },
      (_, i) => ({
        day: daysInPrevMonth - firstDayOfMonth + i + 1,
        currentMonth: false,
        weekDay: i
      })
    );

    const currentMonthDays = Array.from(
      { length: daysInMonth },
      (_, i) => ({
        day: i + 1,
        currentMonth: true,
        weekDay: (firstDayOfMonth + i) % 7
      })
    );

    return [...previousMonthDays, ...currentMonthDays];
  }, [calendarMonth, calendarYear]);

  // Daily Challenge Tracker data (matching the first image)
  const challengeTrackerData = useMemo(() => [
    { day: 'Sunday', completed: 4, total: 2 },
    { day: 'Monday', completed: 4, total: 2 },
    { day: 'Tuesday', completed: 4, total: 3 },
    { day: 'Wednesday', completed: 4, total: 2 },
    { day: 'Thursday', completed: 4, total: 1 },
    { day: 'Friday', completed: 4, total: 2 },
    { day: 'Saturday', completed: 4, total: 2 }
  ], []);

  // Progress Over Time data (matching the second image)
  const progressOverTimeData = useMemo(() => [
    { month: 'Jan', avgWOL: 2, challengesCompleted: 3 },
    { month: 'Feb', avgWOL: 3, challengesCompleted: 3 },
    { month: 'Mar', avgWOL: 4, challengesCompleted: 5 },
    { month: 'Apr', avgWOL: 3, challengesCompleted: 4 },
    { month: 'May', avgWOL: 6, challengesCompleted: 6 },
    { month: 'Jun', avgWOL: 6, challengesCompleted: 7 },
    { month: 'Jul', avgWOL: 5, challengesCompleted: 6 }
  ], []);

  // Top 5 Wheel of Life Categories data
  const top_categories = [
    { name: 'Emotional', points: '1,200pts', percentage: '+8.2%', color: '#47AEA9', value: 35 },
    { name: 'Spiritual', points: '800pts', percentage: '+7%', color: '#91BF2C', value: 20 },
    { name: 'Family', points: '645pts', percentage: '+2.5%', color: '#A1D431', value: 15 },
    { name: 'Physical', points: '590pts', percentage: '-6.5%', color: '#E2F2BF', value: 15 },
    { name: 'Career', points: '342pts', percentage: '+1.7%', color: '#F1F9E0', value: 15 }
  ];

  const my_rewards = [
    { name: 'Finance', value: '+8.2%', icon: DarkGreenCheck },
    { name: 'Romance', value: '+8.2%', icon: RedCheck },
    { name: 'Family', value: '+8.2%', icon: BlueCheck },
    { name: 'Creative', value: '+8.2%', icon: YellowCheck },
    { name: 'Physical', value: '+8.2%', icon: GreenCheck },
    { name: 'Social', value: '+8.2%', icon: RedCheck },
    { name: 'Career', value: '+8.2%', icon: DarkBlueCheck },
    { name: 'Emotional', value: '+8.2%', icon: YellowCheck },
    { name: 'Spiritual', value: '+8.2%', icon: PurpleCheck }
  ];

  const teal_colors = ["#47AEA9", "#91BF2C", "#A1D431", "#E2F2BF", "#F1F9E0"];

  // Challenge progress data (from image)
  const dailyChallenges = useMemo(() => [
    { name: 'My Daily Tasks', value: 8, total: 10, color: '#38B2AC' },
    { name: 'Decision Destination', value: 4, total: 10, color: '#38B2AC' },
    { name: '33 Questions', value: 1, total: 10, color: '#B2F5EA' },
  ], []);

  // Chart configuration constants
  const chart_config = {
    yAxisDomain: [0, 8],
    yAxisTicks: [0, 2, 4, 6, 8],
    barSize: 40,
    margins: { top: 20, right: 10, left: -20, bottom: 30 }
  };

  return (
    <div>
      <div className='flex justify-between'>
        <div className="flex max-xl:flex-col justify-between gap-5 w-full">
          {/* Wheel of Life Summary */}
          <div className="bg-white p-5 shadow-md border border-border xl:max-w-[400px] w-full flex flex-col">
            <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Wheel of Life Summary</h2>
            <div className="flex justify-center relative w-full">
              <div className="w-[350px] h-[280px] mx-auto">
                <PolarArea
                  data={{
                    labels: wheelOfLifeData.map((d) => d.name),
                    datasets: [
                      {
                        data: wheelOfLifeData.map((d) => d.value),
                        backgroundColor: wheelOfLifeData.map((d) => d.color),
                        borderWidth: 0,
                        borderColor: '#fff',
                        hoverBackgroundColor: wheelOfLifeData.map((d) => d.color.replace(')', ', 0.8)').replace('rgb', 'rgba')),
                        hoverBorderColor: '#fff',
                        hoverBorderWidth: 2,
                      },
                    ],
                  }}
                  options={{
                    plugins: {
                      legend: { display: false },
                      tooltip: {
                        enabled: true,
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#333',
                        borderColor: '#ddd',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                          label: function (context: any) {
                            return `${context.label}: ${context.raw}/10`;
                          }
                        }
                      },
                      datalabels: { display: false },
                    },
                    scales: {
                      r: {
                        angleLines: { display: false },
                        grid: { display: false },
                        pointLabels: { display: false },
                        ticks: { display: false },
                        min: 0,
                        max: 10,
                      },
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    layout: {
                      padding: 0
                    },
                    onHover: (event: any, elements: any) => {
                      if (event.native) {
                        event.native.target.style.cursor = elements.length ? 'pointer' : 'default';
                      }
                    }
                  }}
                  plugins={[polarAreaLabelLines]}
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-x-2 gap-y-1 mb-3 w-full text-sm">
              {wheelOfLifeData.map((category, index) => (
                <div
                  key={index}
                  className="flex items-center px-2 py-1 rounded-md hover:bg-gray-50 cursor-pointer transition-colors duration-200"
                >
                  <span className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: category.color }}></span>
                  <span className="truncate">{category.name}</span>
                </div>
              ))}
            </div>
            <div className='flex mx-auto'>
              <button className="py-2 px-10 border border-darkBlueNormal text-darkBlueNormal rounded-lg text-sm font-medium hover:bg-blue-50 transition mt-1">Retake Quiz</button>
            </div>
          </div>

          {/* Daily Health Stats */}
          <div className="bg-white p-5 shadow-md border border-border xl:max-w-[400px] w-full">
            <h2 className="text-paragraphContent text-body-lg font-bold mb-2">My Daily Health Stats</h2>
            <div className='flex flex-col justify-between w-full'>
              <div className="space-y-5 w-full mt-3">
                <div className="bg-red-50 py-3 rounded-3xl flex flex-col">
                  <div className="text-lg text-wheelSocial mb-1 flex items-center justify-start gap-1 max-w-[200px] mx-auto w-full"><span><Heart size={20} /></span> Heart Rate</div>
                  <div className="flex items-baseline mx-auto">
                    <span className="text-3xl font-bold text-wheelSocial text-headline-large">{healthStats.heartRate}</span>
                    <span className="text-base text-wheelSocial ml-1">bpm</span>
                  </div>
                </div>
                <div className="bg-blue-50 py-3 rounded-3xl flex flex-col">
                  <div className="text-lg text-[#365697] mb-1 flex items-center justify-start gap-1 max-w-[200px] mx-auto w-full"><span><Zap size={20} /></span> Sleep</div>
                  <div className="flex items-baseline mx-auto">
                    <span className="text-3xl font-bold text-[#365697] text-headline-large">{healthStats.sleep}</span>
                    <span className="text-base text-[#365697] ml-1">/8 steps</span>
                  </div>
                </div>
                <div className="bg-green-50 p-3 rounded-3xl flex flex-col items-center">
                  <div className="text-lg text-wheelPhysical mb-1 flex items-center justify-start gap-1 max-w-[200px] mx-auto w-full"><span><Flag size={20} /></span> Activity</div>
                  <div className="flex items-baseline mx-auto">
                    <span className="text-3xl font-bold text-wheelPhysical text-headline-large">{healthStats.steps}</span>
                    <span className="text-base text-wheelPhysical ml-1">steps</span>
                  </div>
                </div>
              </div>
              <div className='flex mx-auto mt-10'>
                <button className="py-2 px-10 border border-darkBlueNormal text-darkBlueNormal rounded-lg text-sm font-medium hover:bg-blue-50 transition mt-1">View Health Stats</button>
              </div>
            </div>
          </div>

          {/* Daily Challenges */}
          <div className="bg-white p-5 shadow-md border border-border flex justify-between w-full h-full">
            <div className="w-full flex max-2xl:flex-col max-xl:flex-row justify-between">
              <div className="flex flex-col max-w-[380px]">
                <h2 className="text-lg font-bold mb-2">My Daily Challenges</h2>
                <div className="flex items-center justify-between mb-2 mt-2 px-5">
                  <button onClick={handlePrevMonth} className="p-1 rounded-full bg-white border border-gray-200 hover:bg-blue-50 transition flex items-center justify-center shadow-xl w-10 h-10"><ChevronLeft size={18} /></button>
                  <span className="font-medium text-gray-700">{new Date(calendarYear, calendarMonth).toLocaleString('default', { month: 'long', year: 'numeric' })}</span>
                  <button onClick={handleNextMonth} className="p-1 rounded-full bg-white border border-gray-200 hover:bg-blue-50 transition flex items-center justify-center shadow-xl w-10 h-10"><ChevronRight size={18} /></button>
                </div>
                <div className="grid grid-cols-7 mb-2 mt-5">
                  {daysOfWeek.map((day, index) => (
                    <div key={index} className="text-center text-xs text-gray-500 font-medium">
                      {day}
                    </div>
                  ))}
                </div>
                <div className="grid grid-cols-7 gap-1 flex-grow grid-rows-6 h-full">
                  {calendarDays.map((dayObj, index) => {
                    const isSunday = dayObj.weekDay === 0;
                    const isToday = dayObj.currentMonth && dayObj.day === todayDate.getDate() && calendarMonth === todayDate.getMonth() && calendarYear === todayDate.getFullYear();
                    return (
                      <div
                        key={index}
                        className={`text-center text-xs aspect-square min-h-[2.5rem] flex items-center justify-center rounded-md
                          ${!dayObj.currentMonth
                            ? 'text-gray-300'
                            : isToday
                              ? 'bg-blue-500 text-white font-bold'
                              : isSunday && dayObj.currentMonth
                                ? 'text-[#FF0000] font-bold'
                                : 'text-gray-700'}
                          ${dayObj.currentMonth && !isToday && !isSunday ? 'hover:bg-gray-50 cursor-pointer transition-colors' : ''}
                          ${dayObj.currentMonth && isSunday && !isToday ? 'hover:bg-red-200 cursor-pointer transition-colors' : ''}
                        `}
                      >
                        {dayObj.day}
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className="w-full flex flex-col justify-between max-w-[250px] max-2xl:mx-auto">
              <div className="flex-grow">
                <h3 className="text-sm font-bold mb-4 xl:mt-16 lg:mt-10">Daily Challenges</h3>
                <div className="2xl:space-y-16 sm:space-y-10">
                  {dailyChallenges.map((c, i) => (
                    <div key={`challenge-${i}`} className="flex flex-col">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-800 font-medium">{c.name}</span>
                        <span className="text-xs text-gray-500 font-semibold">{c.value}/{c.total}</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div
                          className="h-full rounded-full transition-all duration-500 ease-in-out"
                          style={{ width: `${(c.value / c.total) * 100}%`, background: c.color }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <button className="py-2 px-10 border border-darkBlueNormal text-darkBlueNormal rounded-lg text-sm font-medium hover:bg-blue-50 transition">View Daily Challenges</button>
            </div>
            </div>
          </div>
        </div>
      </div>

      <div className='grid grid-cols-1 2xl:grid-cols-2 gap-6 mt-6'>
        {/* Daily Challenge Tracker */}
        <div className="bg-white p-5 shadow-sm border border-border">
          <h2 className="text-lg font-bold mb-2">My Daily Challenge Tracker</h2>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={challengeTrackerData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                barSize={50}
                barGap={0}
                barCategoryGap={10}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" vertical={true} horizontal={true} />
                <XAxis
                  dataKey="day"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6B7280' }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  domain={[0, 8]}
                  ticks={[0, 2, 4, 6, 8]}
                  tick={{ fontSize: 12, fill: '#6B7280' }}
                />
                <Tooltip
                  cursor={false}
                  contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}
                  formatter={(value, name) => [
                    name === 'completed' ? `${value} Completed` : `${value} Total`,
                    name === 'completed' ? 'Completed Challenges' : 'Total Challenges'
                  ]}
                />
                <Bar
                  dataKey="total"
                  stackId="a"
                  fill="#47AEA9"
                  radius={[0, 0, 0, 0]}
                />
                <Bar
                  dataKey="completed"
                  stackId="a"
                  fill="#47AEA9CC"
                  radius={[0, 0, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center mt-4 space-x-6">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-[#4FD1C5] mr-2 rounded-sm"></div>
              <span className="text-xs text-gray-600">Completed Challenges</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-[#B2F5EA] mr-2 rounded-sm"></div>
              <span className="text-xs text-gray-600">Total Challenges</span>
            </div>
          </div>
        </div>

        {/* Progress Over Time */}
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold mb-4">My Progress Over Time</h2>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={progressOverTimeData}
                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" vertical={true} horizontal={true} />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6B7280' }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  domain={[0, 8]}
                  ticks={[0, 2, 4, 6, 8]}
                  tick={{ fontSize: 12, fill: '#6B7280' }}
                />
                <Tooltip
                  cursor={{ stroke: '#ddd', strokeWidth: 1, strokeDasharray: '3 3' }}
                  contentStyle={{ borderRadius: '8px', border: 'none', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}
                  formatter={(value, name) => [
                    `${value}`,
                    name === 'avgWOL' ? 'Avg WOL Response' : 'Challenges Completed'
                  ]}
                />
                <Line
                  type="linear"
                  dataKey="avgWOL"
                  stroke="#4FD1C5"
                  strokeWidth={2}
                  dot={{ r: 4, fill: "#4FD1C5", strokeWidth: 0 }}
                  activeDot={{ r: 6, fill: "#4FD1C5", strokeWidth: 0 }}
                  connectNulls={true}
                />
                <Line
                  type="linear"
                  dataKey="challengesCompleted"
                  stroke="#F56565"
                  strokeWidth={2}
                  dot={{ r: 4, fill: "#F56565", strokeWidth: 0 }}
                  activeDot={{ r: 6, fill: "#F56565", strokeWidth: 0 }}
                  connectNulls={true}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center mt-4 space-x-6">
            <div className="flex items-center">
              <div className="w-2 h-1 bg-[#4FD1C5]"></div>
              <div className="w-2 h-2 bg-[#4FD1C5] rounded-full"></div>
              <div className="w-2 h-1 bg-[#4FD1C5] mr-2"></div>
              <span className="text-xs text-gray-600">Avg WOL Response</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-1 bg-[#F56565]"></div>
              <div className="w-2 h-2 bg-[#F56565] rounded-full"></div>
              <div className="w-2 h-1 bg-[#F56565] mr-2"></div>
              <span className="text-xs text-gray-600">Challenges Completed</span>
            </div>
          </div>
        </div>
      </div>
      <div className='grid grid-cols-1 2xl:grid-cols-2 gap-6 mt-6'>
        {/* My Rewards */}
        <div className="bg-white p-4 shadow-md border border-border">
          <h2 className="text-paragraphContent text-body-lg font-bold mb-4">My Health Stats</h2>
          <div className="space-y-1">
            {my_rewards.map((stat, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="border border-border rounded-full p-1 bg-landingBackground mr-3"><stat.icon /></div>
                  <span className="text-sm text-gray-700">{stat.name}</span>
                </div>
                <div className='px-3 py-1 bg-landingBackground rounded-full'>
                  <span className="text-body-medium text-paragraphContent">{stat.value}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top 5 Wheel of Life Categories */}
        <div className="bg-white p-4 shadow-md border border-border">
          <h2 className="text-lg font-semibold mb-4">My Top 5 Wheel of Life Categories</h2>
          <DonutChart
            title=""
            colors={teal_colors}
            data={top_categories}
          />
        </div>
      </div>
    </div>
  );
}