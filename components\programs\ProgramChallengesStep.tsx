import React, { useState } from "react";
import { Green<PERSON>he<PERSON>, RedChe<PERSON>, LightRedCheck, BrownCheck, BlueCheck, DarkBlueCheck, YellowCheck, PurpleCheck, DarkGreenCheck, SixDots } from '@/utils/icons';
import { Button } from "@/components/ui/button";

export default function ProgramChallengeStep({
  challenges,
  setChallenges,
  lockOrder,
  setLockOrder,
  onBack,
  onNext
}: {
  challenges: any[];
  setChallenges: (challenges: any[]) => void;
  lockOrder: boolean;
  setLockOrder: (v: boolean) => void;
  onBack: () => void;
  onNext: () => void;
}) {
  const [selectedChallenges, setSelectedChallenges] = useState<number[]>([]);
  const [isOrderLocked, setIsOrderLocked] = useState(false);
  const [dragIndex, setDragIndex] = useState<number | null>(null);

  const toggleSelectChallenge = (index: number) => {
    setSelectedChallenges((prev) => {
      if (prev.includes(index)) {
        return prev.filter((i) => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // Only selected cards are draggable and reorderable
  const handleDragStart = (index: number) => {
    setDragIndex(index);
  };
  const handleDragOver = (index: number) => {
    if (dragIndex === null || dragIndex === index) return;
    setSelectedChallenges((prev) => {
      const updated = [...prev];
      const [removed] = updated.splice(dragIndex, 1);
      updated.splice(index, 0, removed);
      return updated;
    });
    setDragIndex(index);
  };
  const handleDragEnd = () => {
    setDragIndex(null);
  };

  // Sort: selected challenges first (in selection order), then unselected
  const selectedChallengeObjs = selectedChallenges.map(id => challenges.find(c => c.id === id)).filter(Boolean);
  const unselectedChallengeObjs = challenges.filter(c => !selectedChallenges.includes(c.id));
  const sortedChallenges = [...selectedChallengeObjs, ...unselectedChallengeObjs];

  return (
    <div className="mt-5 px-2 sm:px-4 md:px-8 pb-8 pt-2 text-left mx-auto w-full sm:max-w-[530px]">
      <div className="mb-4 border-b border-gray-200 pb-2">
        <span className="text-lg sm:text-xl font-bold text-gray-900 block mb-1">Select Challenges:</span>
        <div className="flex items-center gap-2 mb-4">
          <button
            type="button"
            aria-pressed={lockOrder}
            onClick={() => setLockOrder(!lockOrder)}
            className={`relative w-10 h-5 sm:w-12 sm:h-6 rounded-full transition-colors duration-200 focus:outline-none ${lockOrder ? "bg-[#47AEA9]" : "bg-gray-300"}`}
          >
            <span
              className={`absolute left-0 top-0 w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center transition-transform duration-200 bg-white shadow ${lockOrder ? "translate-x-5 sm:translate-x-6" : ""}`}
            >
              {lockOrder ? (
                <span className="text-black text-base sm:text-lg font-sm">✔</span>
              ) : (
                <span className="text-gray-400 text-base sm:text-lg font-bold">✕</span>
              )}
            </span>
          </button>
          <span className="text-sm sm:text-base font-semibold text-gray-700 select-none">
            Lock the order of the challenges
          </span>
        </div>
      </div>
      <div className="space-y-4">
        {sortedChallenges.map((challenge, idx) => {
          const isSelected = selectedChallenges.includes(challenge.id);
          const selectionIndex = selectedChallenges.indexOf(challenge.id);
          // Only selected cards are draggable and reorderable
          const draggable = lockOrder && isSelected;
          return (
            <div
              className={`${lockOrder ? 'flex' : ''}`}
              key={challenge.id}
              onClick={() => toggleSelectChallenge(challenge.id)}
              draggable={draggable}
              onDragStart={draggable ? () => handleDragStart(selectionIndex) : undefined}
              onDragOver={draggable ? (e) => { e.preventDefault(); handleDragOver(selectionIndex); } : undefined}
              onDragEnd={draggable ? handleDragEnd : undefined}
              style={{ cursor: draggable ? 'grab' : 'pointer' }}
            >
              <div className={`${lockOrder ? 'flex flex-col items-center my-auto mr-2 sm:mr-4' : 'hidden'}`} style={{ width: '32px' }}>
                <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 border-primary flex items-center justify-center font-bold text-black text-base bg-white">
                  {isSelected ? selectionIndex + 1 : ''}
                </div>
                <div className="flex flex-col items-center mt-3 sm:mt-5 gap-0.5">
                  <SixDots />
                </div>
              </div>
              <div
                className={`bg-white border rounded-lg p-3 sm:p-4 flex flex-col relative flex-1 ${isSelected ? 'border-2 border-primary' : 'border-gray-300'}`}
              >
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-bold text-gray-900 text-base sm:text-lg">{challenge.title}</span>
                  {!lockOrder && <span className="ml-auto text-gray-400 text-xs">•••</span>}
                </div>
                <div className="flex items-center gap-2 mb-1 flex-wrap">
                  <span className="text-xs sm:text-sm text-gray-500">Areas of Life:</span>
                  <div className="flex -space-x-1 flex-wrap">
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><GreenCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><RedCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><LightRedCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><BrownCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><BlueCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><BlueCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><DarkBlueCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><YellowCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><PurpleCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><DarkGreenCheck /></div>
                  </div>
                </div>
                <div className="flex flex-wrap gap-4 text-xs sm:text-sm font-bold text-gray-700 mt-1">
                  <span>Steps: {challenge.steps}</span>
                  <span>Duration: {challenge.duration} Days</span>
                  <span>Reward: {challenge.reward} Points</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-0 mt-8">
        <Button
          type="submit"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={onBack}
        >Back</Button>
        <Button
          type="submit"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={onNext}
        >Preview</Button>
      </div>
    </div>
  );
}