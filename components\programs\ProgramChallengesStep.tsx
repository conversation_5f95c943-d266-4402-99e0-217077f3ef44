import React from "react";
import { SixDots,ThreeDots } from '@/utils/icons';
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

export default function ProgramChallengeStep({
  challenges,
  setChallenges,
  lockOrder,
  setLockOrder,
  areasOfLifeIcons,
  onBack,
  onNext
}: {
  challenges: any[];
  setChallenges: (challenges: any[]) => void;
  lockOrder: boolean;
  setLockOrder: (v: boolean) => void;
  areasOfLifeIcons: React.ReactElement[];
  onBack: () => void;
  onNext: () => void;
}) {
  const toggleSelectChallenge = (challengeId: string) => {
    setChallenges(challenges.map(challenge =>
      challenge.id === challengeId
        ? { ...challenge, selected: !challenge.selected }
        : challenge
    ));
  };

  // Filter logic based on lockOrder (Select challenges switch)
  const selectedChallenges = challenges.filter(c => c.selected);
  const unselectedChallenges = challenges.filter(c => !c.selected);

  // When lockOrder is false (switch OFF): show all challenges in original order
  // When lockOrder is true (switch ON): show all challenges but selected ones first
  const sortedChallenges = lockOrder ? [...selectedChallenges, ...unselectedChallenges] : challenges;

  return (
    <div className="mt-5 px-2 sm:px-4 md:px-8 pb-8 pt-2 text-left mx-auto w-full md:max-w-[650px]">
      <div className="mb-4 border-b border-gray-200 pb-2">
        <span className="text-lg sm:text-xl font-bold text-paragraphContent block mb-1">Select Challenges:</span>
        <div className="flex items-center gap-2 py-4">
          <Switch
            checked={lockOrder}
            onCheckedChange={setLockOrder}
          />
          <span className="text-sm sm:text-base font-semibold text-paragraphContent select-none">
            Lock the order of the challenges
          </span>
        </div>
      </div>
      <div className="space-y-4">
        {sortedChallenges.map((challenge) => {
          const isSelected = challenge.selected;
          const selectionIndex = selectedChallenges.findIndex(c => c.id === challenge.id);
          return (
            <div
              className={`${lockOrder ? 'flex' : ''}`}
              key={challenge.id}
              onClick={() => toggleSelectChallenge(challenge.id)}
              style={{ cursor: 'pointer' }}
            >
              <div className={`${lockOrder ? 'flex flex-col items-center my-auto mr-2 sm:mr-4' : 'hidden'}`} style={{ width: '32px' }}>
                <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 border-primary flex items-center justify-center font-bold text-black text-base bg-white">
                  {isSelected ? selectionIndex + 1 : ''}
                </div>
                <div className="flex flex-col items-center mt-3 sm:mt-5 gap-0.5">
                  <SixDots />
                </div>
              </div>
              <div
                className={`bg-white border rounded-lg p-3 sm:p-5 flex flex-col relative flex-1 ${isSelected ? 'border-2 border-primary' : 'border-gray-300'}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-bold text-paragraphContent text-base sm:text-lg">{challenge.title}</span>
                  {!lockOrder && <ThreeDots />}
                </div>
                <div className="flex items-center gap-2 mb-1 flex-wrap">
                  <span className="text-xs sm:text-sm text-paragraphContent">Areas of Life:</span>
                  <div className="flex -space-x-1 flex-wrap">
                    {areasOfLifeIcons.map((icon, index) => (
                      <div key={index} className='border border-border rounded-full p-1 bg-landingBackground'>
                        {icon}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex flex-wrap gap-4 text-xs sm:text-sm font-bold text-paragraphContent mt-1">
                  <span>Steps: {challenge.steps}</span>
                  <span>Duration: {challenge.duration}</span>
                  <span>Reward: {challenge.reward}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-0 mt-8">
        <Button
          variant="netural"
          size="sm"
          className="text-lightBlue h-8 w-full sm:w-auto px-6"
          onClick={onBack}
        >
          <span className="text-body-medium">Back</span>
        </Button>
        <Button
          variant="netural"
          size="sm"
          className="text-lightBlue h-8 w-full sm:w-auto px-6"
          onClick={onNext}
        >
          <span className="text-body-medium">Next</span>
        </Button>
      </div>
    </div>
  );
}