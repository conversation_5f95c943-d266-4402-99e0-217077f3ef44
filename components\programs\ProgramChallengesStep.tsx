import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ed<PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON>, Blue<PERSON>heck, DarkBlueCheck, YellowChe<PERSON>, Purple<PERSON>he<PERSON>, DarkGreenCheck, SixDots } from '@/utils/icons';
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

export default function ProgramChallengeStep({
  challenges,
  setChallenges,
  lockOrder,
  setLockOrder,
  onBack,
  onNext
}: {
  challenges: any[];
  setChallenges: (challenges: any[]) => void;
  lockOrder: boolean;
  setLockOrder: (v: boolean) => void;
  onBack: () => void;
  onNext: () => void;
}) {
  const toggleSelectChallenge = (challengeId: string) => {
    setChallenges(challenges.map(challenge =>
      challenge.id === challengeId
        ? { ...challenge, selected: !challenge.selected }
        : challenge
    ));
  };

  // Sort: selected challenges first only if lock<PERSON>rder is true, otherwise show all challenges
  const selectedChallenges = challenges.filter(c => c.selected);
  const unselectedChallenges = challenges.filter(c => !c.selected);
  const sortedChallenges = lockOrder ? [...selectedChallenges, ...unselectedChallenges] : challenges;

  return (
    <div className="mt-5 px-2 sm:px-4 md:px-8 pb-8 pt-2 text-left mx-auto w-full md:max-w-[650px]">
      <div className="mb-4 border-b border-gray-200 pb-2">
        <span className="text-lg sm:text-xl font-bold text-gray-900 block mb-1">Select Challenges:</span>
        <div className="flex items-center gap-2 mb-4">
          <Switch
            checked={lockOrder}
            onCheckedChange={setLockOrder}
          />
          <span className="text-sm sm:text-base font-semibold text-gray-700 select-none">
            Select challenges
          </span>
        </div>
      </div>
      <div className="space-y-4">
        {sortedChallenges.map((challenge) => {
          const isSelected = challenge.selected;
          const selectionIndex = selectedChallenges.findIndex(c => c.id === challenge.id);
          return (
            <div
              className={`${lockOrder ? 'flex' : ''}`}
              key={challenge.id}
              onClick={lockOrder ? () => toggleSelectChallenge(challenge.id) : undefined}
              style={{ cursor: lockOrder ? 'pointer' : 'default' }}
            >
              <div className={`${lockOrder ? 'flex flex-col items-center my-auto mr-2 sm:mr-4' : 'hidden'}`} style={{ width: '32px' }}>
                <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 border-primary flex items-center justify-center font-bold text-black text-base bg-white">
                  {isSelected ? selectionIndex + 1 : ''}
                </div>
                <div className="flex flex-col items-center mt-3 sm:mt-5 gap-0.5">
                  <SixDots />
                </div>
              </div>
              <div
                className={`bg-white border rounded-lg p-3 sm:p-4 flex flex-col relative flex-1 ${isSelected && lockOrder ? 'border-2 border-primary' : 'border-gray-300'}`}
              >
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-bold text-gray-900 text-base sm:text-lg">{challenge.title}</span>
                  {!lockOrder && <span className="ml-auto text-gray-400 text-xs">•••</span>}
                </div>
                <div className="flex items-center gap-2 mb-1 flex-wrap">
                  <span className="text-xs sm:text-sm text-gray-500">Areas of Life:</span>
                  <div className="flex -space-x-1 flex-wrap">
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><GreenCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><RedCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><LightRedCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><BrownCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><BlueCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><BlueCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><DarkBlueCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><YellowCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><PurpleCheck /></div>
                    <div className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'><DarkGreenCheck /></div>
                  </div>
                </div>
                <div className="flex flex-wrap gap-4 text-xs sm:text-sm font-bold text-gray-700 mt-1">
                  <span>Steps: {challenge.steps}</span>
                  <span>Duration: {challenge.duration} Days</span>
                  <span>Reward: {challenge.reward} Points</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-0 mt-8">
        <Button
          type="submit"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={onBack}
        >Back</Button>
        <Button
          type="submit"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={onNext}
        >Preview</Button>
      </div>
    </div>
  );
}