"use client"

import { useState, useEffect } from "react"
import { CircularProgressChart } from "@/components/dashboard/CircularProgressChart"
import { DonutChart } from "@/components/dashboard/DonutChart"

export function Patient() {
  const [activeTab, setActiveTab] = useState("overview")
  const [isMobile, setIsMobile] = useState(false)

  // Define the colors for the charts
  const tealColors = ["#E2F2BF", "#799F25", "#A1D431", "#91BF2C", "#799F25"];
  const goldColors = ["#EDBA29", "#B28C1F", "#D5A725", "#F9EABD", "#BE9521"];

  // Check window width on client side only
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    // Set initial value
    handleResize()

    // Add event listener
    window.addEventListener('resize', handleResize)

    // Clean up
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className="max-md:p-0">
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
        <DonutChart
          title="Struggling Patients"
          colors={goldColors}
          data={[
            { name: "Sarah Smith", points: "1,200pts", percentage: "****%", color: goldColors[0] },
            { name: "Max Johnson", points: "800pts", percentage: "+7%", color: goldColors[1] },
            { name: "Emily Park", points: "645pts", percentage: "****%", color: goldColors[2] },
            { name: "Alex Chen", points: "590pts", percentage: "-6.5%", color: goldColors[3] },
            { name: "Ryan Taylor", points: "342pts", percentage: "****%", color: goldColors[4] }
          ]}
        />

        <DonutChart
          title="Performing Patients"
          colors={tealColors}
          data={[
            { name: "Olivia White", points: "1,200pts", percentage: "****%", color: tealColors[0] },
            { name: "Dylan Patel", points: "800pts", percentage: "+7%", color: tealColors[1] },
            { name: "Grace Thompson", points: "645pts", percentage: "****%", color: tealColors[2] },
            { name: "Ethan Martinez", points: "590pts", percentage: "-6.5%", color: tealColors[3] },
            { name: "Jasmine Rogers", points: "342pts", percentage: "****%", color: tealColors[4] }
          ]}
        />
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6 md:mb-8 mt-5">
        <CircularProgressChart
          title="Jenny Marin - Weekly Progress"
          percentage={67}
          color="#3ABAB4"
          linkText="View Patient Dashboard"
          linkHref="/dashboard/patients"
          legend={[
            { label: "Completed", color: "#3ABAB4" },
            { label: "Remaining", color: "#E5E7EB" }
          ]}
        />

        <CircularProgressChart
          title="Sarah James - Weekly Progress"
          percentage={67}
          color="#4F6D8F"
          linkText="View Patient Dashboard"
          linkHref="/dashboard/challenges"
          legend={[
            { label: "Completed", color: "#4F6D8F" },
            { label: "Remaining", color: "#E5E7EB" }
          ]}
        />

        <CircularProgressChart
          title="Devon Carter - Weekly Progress"
          percentage={67}
          color="#D4A72C"
          linkText="View Patient Dashboard"
          linkHref="/dashboard/programs"
          legend={[
            { label: "Completed", color: "#D4A72C" },
            { label: "Remaining", color: "#E5E7EB" }
          ]}
        />
      </div>
    </div>
  )
}
