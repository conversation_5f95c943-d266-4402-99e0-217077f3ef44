export const Programs = [
  {
    id: 0,
    title: 'Welcome to Peakality',
    image: '/dashboard/driver.svg',
    instructor: {
      name: "<PERSON>",
      courses: 7,
      bio: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas."
    },
    description: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas. Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas.",
    challenges: [
      { id: 0, title: 'Wheel of Life Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 1, title: '33 Questions Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 2, title: 'Decision Destination', steps: 30, duration: 30, reward: 900, progress: 0 },
    ],
  },
  {
    id: 1,
    title: 'Sarah’s Program',
    image: '/dashboard/pray.svg',
    instructor: {
      name: "",
      courses: 7,
      bio: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas."
    },
    description: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas. Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas.",
    challenges: [
      { id: 0, title: 'Wheel of Life Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 1, title: '33 Questions Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 2, title: 'Decision Destination', steps: 30, duration: 30, reward: 900, progress: 0 },
    ],
  },
  {
    id: 2,
    title: 'Sarah’s Program',
    image: '/dashboard/climb.svg',
    instructor: {
      name: "Court Potter",
      courses: 7,
      bio: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas."
    },
    description: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas. Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas.",
    challenges: [
      { id: 0, title: 'Wheel of Life Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 1, title: '33 Questions Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 2, title: 'Decision Destination', steps: 30, duration: 30, reward: 900, progress: 0 },
    ],
  },
  {
    id: 3,
    title: 'Sarah’s Program',
    image: '/dashboard/sea.svg',
    instructor: {
      name: "Court Potter",
      courses: 7,
      bio: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas."
    },
    description: "Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas. Vestibulum augue proin enim cursus montes, ante. Ultrices posuere mattis elit egestas aliquam pellentesque nisl id quis. Turpis volutpat tincidunt volutpat lectus quam facilisi nibh augue egestas.",
    challenges: [
      { id: 0, title: 'Wheel of Life Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 1, title: '33 Questions Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
      { id: 2, title: 'Decision Destination', steps: 30, duration: 30, reward: 900, progress: 0 },
    ],
  }
];