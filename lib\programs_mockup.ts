import { CHALLENGES } from './challenges_mockup';

export interface Program {
  id: number;
  title: string;
  image: string;
  description: string;
  instructor: {
    name: string;
    courses: number;
    bio: string;
  };
  challenges: number[]; // Array of challenge IDs that belong to this program
  areas: string[];
  totalDuration: number;
  totalReward: number;
  isPublic: boolean;
}

export const PROGRAMS: Program[] = [
  {
    id: 1,
    title: 'Wheel of Life Challenge',
    image: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    description: 'Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas. Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas.',
    instructor: {
      name: 'Carol Potter',
      courses: 7,
      bio: 'Experienced life coach with over 10 years of experience helping people achieve their goals and find balance in their lives.'
    },
    challenges: [1], // Contains only the Wheel of Life Challenge
    areas: ["Finance", "Love/Relationships", "Family", "Creative", "Physical", "Social", "Professional", "Emotional", "Spiritual"],
    totalDuration: 30,
    totalReward: 300,
    isPublic: true
  },
  {
    id: 2,
    title: '33 Questions Challenge',
    image: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80',
    description: 'Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas. Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui.',
    instructor: {
      name: 'Carol Potter',
      courses: 7,
      bio: 'Experienced life coach with over 10 years of experience helping people achieve their goals and find balance in their lives.'
    },
    challenges: [2], // Contains only the 33 Questions Challenge
    areas: ["Finance", "Love/Relationships", "Family", "Creative", "Physical", "Social", "Professional", "Emotional", "Spiritual"],
    totalDuration: 30,
    totalReward: 300,
    isPublic: true
  },
  {
    id: 3,
    title: 'Decision Destination Challenge',
    image: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
    description: 'Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas.',
    instructor: {
      name: 'Carol Potter',
      courses: 7,
      bio: 'Experienced life coach with over 10 years of experience helping people achieve their goals and find balance in their lives.'
    },
    challenges: [3], // Contains only the Decision Destination Challenge
    areas: ["Finance", "Love/Relationships", "Family", "Creative", "Physical", "Social", "Professional", "Emotional", "Spiritual"],
    totalDuration: 30,
    totalReward: 300,
    isPublic: true
  },
  {
    id: 4,
    title: 'Sarah\'s Complete Program',
    image: '/dashboard/pray.svg',
    description: 'A comprehensive program designed by Sarah that includes multiple challenges to help you grow in different areas of your life. This program combines practical exercises with reflective practices.',
    instructor: {
      name: 'Sarah Johnson',
      courses: 12,
      bio: 'Sarah is a certified life coach and wellness expert with over 15 years of experience. She specializes in holistic personal development and has helped thousands of people transform their lives.'
    },
    challenges: [4, 5, 6], // Contains Just in Case, Pulling it Together, and The Toolkit challenges
    areas: ["Finance", "Love/Relationships", "Family", "Creative", "Physical", "Social", "Professional", "Emotional", "Spiritual"],
    totalDuration: 90, // Sum of all challenge durations
    totalReward: 900, // Sum of all challenge rewards
    isPublic: true
  },
  {
    id: 5,
    title: 'Welcome to Peakality',
    image: '/dashboard/driver.svg',
    description: 'Start your journey with Peakality through this introductory program. Learn the fundamentals and get familiar with our platform while working on essential life skills.',
    instructor: {
      name: 'Peakality Team',
      courses: 25,
      bio: 'The Peakality team consists of experienced coaches, therapists, and personal development experts dedicated to helping you reach your peak potential.'
    },
    challenges: [1, 2, 3], // Contains Wheel of Life, 33 Questions, and Decision Destination
    areas: ["Finance", "Love/Relationships", "Family", "Creative", "Physical", "Social", "Professional", "Emotional", "Spiritual"],
    totalDuration: 90,
    totalReward: 900,
    isPublic: true
  }
];

// Helper function to get challenges for a program
export const getProgramChallenges = (programId: number) => {
  const program = PROGRAMS.find(p => p.id === programId);
  if (!program) return [];
  
  return program.challenges.map(challengeId => 
    CHALLENGES.find(c => c.id === challengeId)
  ).filter(Boolean);
};

// Helper function to get program by ID
export const getProgramById = (id: number) => {
  return PROGRAMS.find(p => p.id === id);
};
