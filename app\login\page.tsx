"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { isValidEmailOrPhone } from "@/lib/validation";
import { useDispatch } from "react-redux";
import { setUser } from "@/store/userSlice";
import { handleLogin, getRedirectPath } from "@/utils/auth";

// Define form validation schema
const loginFormSchema = z.object({
  emailOrPhone: z.string()
    .min(1, { message: "Email or phone is required" })
    .refine(isValidEmailOrPhone, {
      message: "Please enter a valid email address or phone number"
    }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters",
  }),
  rememberMe: z.boolean().default(false),
});

type LoginFormValues = z.infer<typeof loginFormSchema>;

export default function LoginPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  // Initialize form with react-hook-form
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      emailOrPhone: "",
      password: "",
      rememberMe: false,
    },
    mode: "onBlur", // Validate on blur for better user experience
  });

  // Form submission handler
  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true);
    setLoginError(null);

    try {
      // Handle login with our utility function
      const userData = handleLogin(data.emailOrPhone, data.password);

      if (!userData) {
        setLoginError("Invalid email or password");
        return;
      }

      // Store user data in Redux
      dispatch(setUser(userData));

      // Get redirect path based on user role
      const redirectPath = getRedirectPath(userData);
      router.push(redirectPath);
    } catch (error) {
      console.error("Login error:", error);
      setLoginError("An error occurred during login. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex flex-col">
      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Mountain image */}
        <div className="md:w-1/2 max-md:h-[450px] relative">
            <Image
              src="/auth/login.svg"
              alt="Mountain landscape"
              fill
              className="w-full h-full"
              priority
            />
        </div>

        {/* Right side - Login form */}
        <div className="w-full md:w-1/2 flex items-center max-md:justify-center justify-end">
          <div className="w-full max-w-[560px] lg:pr-20 max-xl:px-5">
            <div className="py-10">
              <h1 className="text-display-md text-paragraphContent max-md:text-center">Log In</h1>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                <FormField
                  control={form.control}
                  name="emailOrPhone"
                  render={({ field, fieldState }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel className="text-body-medium text-paragraphContent">Email or Phone</FormLabel>
                      <FormControl>
                        <Input
                          className={`bg-landingBackground border-0 border-b-2 ${
                            fieldState.invalid ? 'border-red-500' : 'border-medium-gray'
                          } rounded-none shadow-none h-12 focus-visible:ring-0 focus-visible:border-gray-500 px-2`}
                          {...field}
                          disabled={isLoading}
                          onBlur={(e) => {
                            field.onBlur();
                            // Additional validation feedback can be added here
                          }}
                        />
                      </FormControl>
                      <FormMessage className="text-red-500" />
                      {!fieldState.invalid && field.value && (
                        <p className="text-xs text-green-600 mt-1">
                          Valid format ✓
                        </p>
                      )}
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field, fieldState }) => (
                    <FormItem className="space-y-1.5">
                      <FormLabel className="text-body-medium text-paragraphContent">Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          className={`bg-landingBackground border-0 border-b-2 ${
                            fieldState.invalid ? 'border-red-500' : 'border-medium-gray'
                          } rounded-none shadow-none h-12 focus-visible:ring-0 focus-visible:border-gray-500`}
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage className="text-red-500" />
                      <p className={`text-xs ${fieldState.invalid ? 'text-red-500' : 'text-gray-500'} mt-1`}>
                        It must be a combination of minimum 8 letters, numbers, and symbols.
                      </p>
                      {!fieldState.invalid && field.value && field.value.length >= 8 && (
                        <p className="text-xs text-green-600 mt-1">
                          Password meets requirements ✓
                        </p>
                      )}
                    </FormItem>
                  )}
                />

                <div className="flex items-center justify-between pt-1">
                  <FormField
                    control={form.control}
                    name="rememberMe"
                    render={({ field }) => (
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="rememberMe"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                          className="border-neturalDark"
                        />
                        <label
                          htmlFor="rememberMe"
                          className="text-sm font-normal text-neturalDark"
                        >
                          Remember me
                        </label>
                      </div>
                    )}
                  />

                  <Link
                    href="/forgot-password"
                    className="text-body-medium text-darkerBlue"
                  >
                    Forgot Password?
                  </Link>
                </div>

                {loginError && (
                  <div className="text-red-500 text-sm mt-2 bg-red-50 p-2 rounded border border-red-200">
                    {loginError}
                  </div>
                )}

                <div className="pt-2 pb-8">
                  <Button
                    type="submit"
                    variant="netural"
                    className="w-full font-normal h-11 rounded-sm"
                    disabled={isLoading}
                  >
                    <span className="text-white text-title-medium">{isLoading ? "Logging in..." : "Log In"}</span>
                  </Button>
                </div>

                <div className="text-sm pt-8 border-t border-gray-200">
                  <p className="py-4">
                    No account yet? <Link href="/signup" className="font-medium underline">Sign Up</Link>
                  </p>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </main>
    </div>
  );
}
