"use client";

import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function GuestPage() {
  const router = useRouter();

  const handleResend = () => {
    router.push("/forgot-password/new-password");
  };

  return (
    <div className="flex flex-col">

      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 relative">
          <Image
            src="/auth/forgot-password.svg"
            alt="People in a golf cart"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Reset Link Sent Message */}
        <div className="w-full md:w-1/2 flex items-center justify-end max-xl:px-5 max-md:justify-center">
          <div className="w-full max-w-[800px] lg:pl-10">
            <div className="max-md:text-center mb-10 max-md:pt-10">
              <h1 className="text-display-md mb-4 max-md:text-3xl">Your reset link was sent</h1>

              <p className="text-body-lg mb-4 text-darkBlueNormalHover max-w-[560px]">
                If an account exists for that email or phone number, you will get an
                email or text message with a link to reset your password.
              </p>

              <p className="text-body-lg mb-8 text-darkBlueNormalHover max-w-[500px]">
                If it doesn't arrive after a few minutes, check your spam folder,
                or <button onClick={handleResend} className="text-gray-600 font-medium underline bg-transparent border-none p-0 cursor-pointer">Click to Resend</button>.
              </p>
            </div>

            <div className="text-sm text-black border-t border-gray-200 py-10">
              No account yet? <Link href="/signup" className="ont-medium underline">Sign Up</Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
