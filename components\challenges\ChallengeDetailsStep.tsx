"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { AreaIcon } from "@/components/dashboard/AreaIcon";
import Image from "next/image";
import { PreviewNewProgram, UploadIcon } from "@/utils/icons";
import { Button } from "@/components/ui/button";

const AREAS = [
  "All Areas",
  "Finance",
  "Love/Relationships",
  "Family",
  "Creative",
  "Physical",
  "Social",
  "Professional",
  "Emotional",
  "Spiritual",
];

const FREQUENCY = ["Daily", "Weekly", "Monthly"];
const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
];
const ALLOWED_VIDEO_TYPES = [
  "video/mp4",
  "video/webm",
  "video/ogg",
  "video/quicktime",
  "video/mov",
];
const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES];

export default function ChallengeDetailsStep({
  recurring,
  setRecurring,
  frequency,
  setFrequency,
  name,
  setName,
  description,
  setDescription,
  instructor,
  setInstructor,
  areas,
  setAreas,
  handleAreaToggle,
  onNext,
  media,
  setMedia,
  onFileChange,
}: any) {
  const [mounted, setMounted] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string>("");
  const [mediaPreview, setMediaPreview] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    description?: string;
    instructor?: string;
    areas?: string;
    media?: string;
  }>({});

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const convertBlobToFile = async () => {
      if (media && media.startsWith("blob:")) {
        try {
          const response = await fetch(media);
          const blob = await response.blob();
          const file = new File([blob], "media-file", { type: blob.type });
          setFile(file);
          setMediaPreview(media);
          if (onFileChange) {
            onFileChange(file);
          }
        } catch (error) {
          console.error("Error converting blob to file:", error);
          setMediaPreview("");
          setFile(null);
        }
      } else if (media) {
        setMediaPreview(media);
      }
    };

    convertBlobToFile();
  }, [media, onFileChange]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError("");
    if (e.target.files && e.target.files[0]) {
      const selected = e.target.files[0];

      if (!ALLOWED_TYPES.includes(selected.type)) {
        setError(
          "Only image and video files (jpg, png, gif, mp4, webm, mov, etc.) are allowed."
        );
        setFile(null);
        setMediaPreview("");
        setMedia("");
        if (onFileChange) onFileChange(null);
        return;
      }

      if (selected.size > 200 * 1024 * 1024) {
        setError("File size must be less than 2MB");
        setFile(null);
        setMediaPreview("");
        setMedia("");
        if (onFileChange) onFileChange(null);
        return;
      }

      setFile(selected);
      const url = URL.createObjectURL(selected);
      setMediaPreview(url);
      setMedia(url);

      if (onFileChange) {
        onFileChange(selected);
      }
    }
  };

  const handleRemove = () => {
    if (mediaPreview && mediaPreview.startsWith("blob:")) {
      URL.revokeObjectURL(mediaPreview);
    }

    setFile(null);
    setMediaPreview("");
    setMedia("");
    setError("");

    if (onFileChange) {
      onFileChange(null);
    }
  };

  const renderPreview = () => {
    if (!mediaPreview) return <PreviewNewProgram />;

    if (
      (file && file.type.startsWith("image/")) ||
      (!file &&
        mediaPreview.match(/^data:image|\.jpg|\.jpeg|\.png|\.gif|\.webp$/))
    ) {
      return (
        <Image
          src={mediaPreview}
          alt="Preview"
          width={88}
          height={65}
          className="rounded object-cover"
        />
      );
    }

    if (
      (file && file.type.startsWith("video/")) ||
      (!file &&
        mediaPreview.match(/^data:video|\.mp4|\.webm|\.ogg|\.mov|\.quicktime$/))
    ) {
      return (
        <video
          src={mediaPreview}
          width={88}
          height={65}
          className="w-full h-full object-cover rounded"
          controls
          preload="metadata"
        />
      );
    }

    return null;
  };

  const validateForm = () => {
    const errors: {
      name?: string;
      description?: string;
      instructor?: string;
      areas?: string;
      media?: string;
    } = {};

    if (!name.trim()) {
      errors.name = "Challenge name is required";
    }

    if (!description.trim()) {
      errors.description = "Challenge description is required";
    } else if (description.length < 10) {
      errors.description = "Description must be at least 10 characters long";
    }

    if (!instructor) {
      errors.instructor = "Please select an instructor";
    }

    if (areas.length === 0) {
      errors.areas = "Please select at least one area";
    }

    if (!mediaPreview && !file) {
      errors.media = "Please upload a photo or video";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  if (!mounted) {
    return null; // or a loading skeleton
  }

  return (
    <div className="flex justify-center w-full">
      <div className="px-2 py-4 sm:p-8 w-full max-w-full sm:max-w-[550px] mt-4 sm:mt-8">
        <div className="text-body-lg font-bold mb-6">
          Fill out Challenge Details:
        </div>
        <div className="flex items-center justify-between mb-4 border-b border-gray-200 pb-4">
          <div className="flex items-center">
            <Switch checked={recurring} onCheckedChange={setRecurring} />
            <span className="ml-2 mr-4 text-body-large text-paragraphContent">
              Make this a recurring challenge
            </span>
          </div>
          <Select 
            value={frequency} 
            onValueChange={setFrequency}
            disabled={!recurring}
          >
            <SelectTrigger className={`bg-landingBackground w-full sm:w-[130px] ${!recurring ? 'opacity-50' : ''}`}>
              <SelectValue placeholder="Select frequency..." />
            </SelectTrigger>
            <SelectContent className="bg-landingBackground">
              {FREQUENCY.map((f) => (
                <SelectItem key={f} value={f}>
                  {f}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="mb-6">
          <div className="relative mb-6">
            <Input
              className={`mb-1 bg-landingBackground w-full ${
                validationErrors.name ? "border-red-500" : ""
              }`}
              placeholder="Name the challenge..."
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
            {validationErrors.name && (
              <p className="text-red-500 text-sm mb-4">
                {validationErrors.name}
              </p>
            )}
          </div>
          <div className="relative mb-6">
            <Textarea
              rows={5}
              className={`mb-1 bg-landingBackground w-full ${
                validationErrors.description ? "border-red-500" : ""
              }`}
              placeholder="Describe the challenge..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
            {validationErrors.description && (
              <p className="text-red-500 text-sm mb-4">
                {validationErrors.description}
              </p>
            )}
          </div>
          <div className="relative mb-6">
            <Select value={instructor} onValueChange={setInstructor}>
              <SelectTrigger
                className={`bg-landingBackground w-full ${
                  validationErrors.instructor ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="Select an instructor..." />
              </SelectTrigger>
              <SelectContent className="bg-landingBackground">
                <SelectItem value="Court Potter">Court Potter</SelectItem>
                <SelectItem value="Jane Doe">Jane Doe</SelectItem>
                <SelectItem value="Court Potter1">Court Potter1</SelectItem>
                <SelectItem value="Jane Doe1">Jane Doe1</SelectItem>
                <SelectItem value="Court Potter2">Court Potter2</SelectItem>
                <SelectItem value="Jane Doe2">Jane Doe2</SelectItem>
                <SelectItem value="Court Potter3">Court Potter3</SelectItem>
                <SelectItem value="Jane Doe3">Jane Doe3</SelectItem>
                <SelectItem value="Court Potter4">Court Potter4</SelectItem>
                <SelectItem value="Jane Doe4">Jane Doe4</SelectItem>
                <SelectItem value="Court Potter5">Court Potter5</SelectItem>
                <SelectItem value="Jane Doe5">Jane Doe5</SelectItem>
                <SelectItem value="Court Potter6">Court Potter6</SelectItem>
                <SelectItem value="Jane Doe6">Jane Doe6</SelectItem>
                <SelectItem value="Court Potter7">Court Potter7</SelectItem>
                <SelectItem value="Jane Doe7">Jane Doe7</SelectItem>
                <SelectItem value="Court Potter8">Court Potter8</SelectItem>
                <SelectItem value="Jane Doe8">Jane Doe8</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.instructor && (
              <p className="text-red-500 text-sm mt-1">
                {validationErrors.instructor}
              </p>
            )}
          </div>
        </div>
        <div className="mb-6">
          <div className="text-body-medium text-paragraphContent font-bold mb-2">
            Select Areas of Life for this Challenge
          </div>
          <div className="flex flex-wrap gap-4 sm:gap-6 justify-center">
            {AREAS.map((area: string, i: number) => (
              <button
                key={i}
                type="button"
                className={`w-6 h-6 bg-gray-200 border border-white -ml-2 first:ml-0 flex items-center justify-center rounded-full transition-all duration-150 ${
                  areas.includes(area) ? "ring-2 ring-blue-500 bg-blue-100" : ""
                }`}
                onClick={() => handleAreaToggle(area)}
                aria-pressed={areas.includes(area)}
              >
                <AreaIcon area={area} />
              </button>
            ))}
          </div>
          {validationErrors.areas && (
            <p className="text-red-500 text-sm mt-2 text-center">
              {validationErrors.areas}
            </p>
          )}
        </div>
        <div className="mt-7 pb-4 mb-8 border-b border-gray-200">
          <div className="flex flex-col">
            <label className="text-body-medium text-paragraphContent font-bold mb-2">
              Choose a Photo or Video
            </label>
            <div className="flex flex-col md:flex-row w-full gap-4 max-md:gap-0">
              <div className="flex w-full md:w-auto">
                <div className="flex justify-center items-center mb-2 md:mb-0 md:mr-4 w-full md:w-24 h-[120px] md:h-24 overflow-hidden">
                  {renderPreview()}
                </div>
                <div className="flex flex-col justify-center w-full md:w-auto">
                  <input
                    type="file"
                    accept="image/*,video/*"
                    className="hidden"
                    id="file-upload"
                    onChange={handleFileChange}
                  />
                  <div className="flex flex-col justify-center items-center">
                    <label
                      htmlFor="file-upload"
                      className="flex items-center gap-2 border border-darkBlueNormal text-body-medium rounded px-4 h-9 text-darkBlueNormal bg-lightBlue cursor-pointer hover:bg-gray-50"
                    >
                      <UploadIcon />
                      Upload
                    </label>
                    <a
                      className={`text-body-medium mt-2 cursor-pointer ${
                        !mediaPreview
                          ? "text-[#AAAAAA]"
                          : "text-paragraphContent"
                      }`}
                      onClick={handleRemove}
                    >
                      remove
                    </a>
                  </div>
                </div>
              </div>

              <div className="hidden md:block w-px bg-darkBlueBackground h-18" />
              <div className="block md:hidden border-t border-gray-200" />
              <div className="flex flex-col md:justify-around gap-2 w-full mt-4 md:mt-0 ml-4">
                <div className="text-paragraphContent flex flex-col justify-center text-body-medium ">
                  <div className="mb-2">Image requirements:</div>
                  <div className="ml-1">1. Min. 400 x 400px</div>
                  <div className="ml-1">2. Max. 2MB</div>
                </div>
              </div>
            </div>
          </div>
          {(error || validationErrors.media) && (
            <div className="text-red-500 text-sm text-center mt-2">
              {error || validationErrors.media}
            </div>
          )}
        </div>
        <div className="flex justify-end">
          <Button
            variant="netural"
            size="sm"
            className="text-lightBlue h-8 w-full sm:w-auto px-6"
            onClick={handleNext}
          >
            <span className="text-body-medium">Next</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
