"use client";

// import { Target, Users } from "lucide-react";
import Image from "next/image";

export function FeatureCards() {
  return (
    <div className="grid md:grid-cols-3 gap-6">
      <div className="rounded-lx border p-5 flex flex-col items-center">
        <Image
          src="/icons/package.svg"
          alt="Package Icon"
          width={40}
          height={60}
          className="mt-3 mb-2"
        />
        <p className="font-bold text-headline-small mb-2 mt-2">
          Clear vs. Vague
        </p>
        <p className="text-title-medium text-[#697077]">
          Over 1,000 scientific studies confirm that setting clear goals boosts
          motivation and persistence.
        </p>
      </div>

      <div className="rounded-lx border border-borderColor p-5 flex flex-col items-center">
        <Image
          src="/icons/target.svg"
          alt="Target Icon"
          width={60}
          height={60}
          className="text-gray-700"
        />
        <p className="font-bold text-headline-small mb-2 mt-2">
          Challenging vs. Easy
        </p>
        <p className="text-title-medium text-[#697077]">
          In 90% of those studies, challenging goals outperformed vague, easy,
          or "do your best" goals.
        </p>
      </div>

      <div className="rounded-lx border p-5 flex flex-col items-center">
        <Image
          src="/icons/users.svg"
          alt="Users Icon"
          width={60}
          height={60}
          className="text-gray-700"
        />
        <p className="font-bold text-headline-small mb-2 mt-2">
          Solo vs. Together
        </p>
        <p className="text-title-medium text-[#697077]">
          One scientific study reported a 95% chance of completing a goal with
          regular partner check-ins.
        </p>
      </div>
    </div>
  );
}
