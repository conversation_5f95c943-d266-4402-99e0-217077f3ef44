# Peakality

A modern patient management and account workflow web application built with Next.js, React, and Tailwind CSS.

## Features
- Responsive landing page with carousel/slider
- Patients table with pagination, sorting, and selection
- Multi-step patient onboarding workflow (details, plan/payment, invite)
- Account management page with profile, billing, and notifications tabs
- Profile photo upload and editable user details
- Accessible, mobile-friendly UI

## Tech Stack
- [Next.js](https://nextjs.org/) (App Router)
- [React](https://react.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Framer Motion](https://www.framer.com/motion/) (for animations)
- TypeScript

## Getting Started

### Prerequisites
- Node.js (v18 or higher recommended)
- npm or yarn

### Installation
```bash
# Clone the repository
 git clone https://github.com/your-username/peakality.git
 cd peakality

# Install dependencies
 npm install
# or
yarn install
```

### Development
```bash
npm run dev
# or
yarn dev
```
Visit [http://localhost:3000](http://localhost:3000) to view the app.

### Build for Production
```bash
npm run build
npm start
# or
yarn build
yarn start
```

## Folder Structure
```
app/                  # Next.js app directory (pages, routes, components)
  └── dashboard/
      ├── account/    # Account management UI
      └── patients/   # Patient management workflow
components/           # Reusable UI components
utils/                # Utility functions and icon components
public/               # Static assets (images, icons)
tailwind.config.js    # Tailwind CSS configuration
```

## Customization & Environment
- Update environment variables in `.env.local` as needed.
- Customize Tailwind styles in `tailwind.config.js`.
- Add your own assets to the `public/` folder.

## License

This project is licensed under the MIT License.