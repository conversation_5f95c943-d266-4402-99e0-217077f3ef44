"use client";

import { useState } from "react";
import ChallengeDetailsStep from "@/components/challenges/ChallengeDetailsStep";
import ChallengeStepsStep from "@/components/challenges/ChallengeStepsStep";
import ChallengePreviewStep from "@/components/challenges/ChallengePreviewStep";
import { PatientPrevBtn, ProgressBtn, ProgressCheck } from "@/utils/icons";
import Link from "next/link";

type StepAnswers = { [stepIdx: number]: { [questionIdx: number]: number } };

const DIFFICULTY = ["Easy", "Medium", "Hard"];
const DURATION = ["5 mins", "10 mins", "15 mins", "30 mins", "1 hour"];
const REWARD = ["10 pts", "20 pts", "30 pts", "50 pts"];
const FREQUENCY = ["Daily", "Weekly", "Monthly"];
const QUESTION_TYPES = ["Valence Scale", "True/False"];

const VALENCE_ICONS = ["😐", "😕", "🙂", "😁", "😃"];
const TRUE_FALSE_ICONS = ["👍", "👎"];

export default function AddChallengePage() {
  const [step, setStep] = useState(0);
  // Challenge details state
  const [recurring, setRecurring] = useState(false);
  const [frequency, setFrequency] = useState(FREQUENCY[0]);
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [instructor, setInstructor] = useState("");
  const [areas, setAreas] = useState<string[]>([]);
  const [media, setMedia] = useState<string>("");
  // Steps state
  const [lockOrder, setLockOrder] = useState(true);
  const [steps, setSteps] = useState<any[]>([]);
  // Step form state
  const [stepForm, setStepForm] = useState({
    name: "",
    difficulty: DIFFICULTY[0],
    duration: DURATION[0],
    reward: REWARD[0],
    media: "",
    addMedia: false,
    addQuestionnaire: false,
    questions: [] as any[],
  });
  // Handlers for details step
  const handleAreaToggle = (key: string) => {
    setAreas((prev) =>
      prev.includes(key) ? prev.filter((a) => a !== key) : [...prev, key]
    );
  };
  const handleMediaChange = (e: any) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setMedia(reader.result as string);
      reader.readAsDataURL(file);
    }
  };
  const handleRemoveMedia = () => setMedia("");
  // Handlers for steps step
  const handleStepMediaChange = (e: any) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () =>
        setStepForm((f) => ({ ...f, media: reader.result as string }));
      reader.readAsDataURL(file);
    }
  };
  const handleRemoveStepMedia = () => setStepForm((f) => ({ ...f, media: "" }));
  const handleAddStep = () => {
    setSteps((prev) => [...prev, { ...stepForm }]);
    setStepForm({
      name: "",
      difficulty: DIFFICULTY[0],
      duration: DURATION[0],
      reward: REWARD[0],
      media: "",
      addMedia: false,
      addQuestionnaire: false,
      questions: [],
    });
  };
  const handleRemoveStep = (idx: number) => {
    setSteps((prev) => prev.filter((_, i) => i !== idx));
  };
  const handleAddQuestion = () => {
    setStepForm((f) => ({
      ...f,
      questions: [
        ...f.questions,
        { text: "", type: QUESTION_TYPES[0], response: "" },
      ],
    }));
  };
  const [activeStep, setActiveStep] = useState(0);

  const handleStepAnswersChange = (updatedSteps: any[]) => {
    setSteps(updatedSteps);
  };

  const handleSave = () => {
    // Log the complete challenge data
    console.log({
      name,
      description,
      instructor,
      areas,
      media,
      steps,
      recurring,
      frequency,
    });
    
    // TODO: Add your API call here to save the challenge
    // Example:
    // try {
    //   await saveChallenge({
    //     name,
    //     description,
    //     instructor,
    //     areas,
    //     media,
    //     steps,
    //     recurring,
    //     frequency,
    //   });
    //   // Handle success (e.g., redirect to challenges list)
    // } catch (error) {
    //   // Handle error
    // }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <div>
        <p className="text-display-md text-paragraphContent">New Challenge</p>
        <Link
          href="/dashboard/challenges"
          className="flex items-center mt-6 pb-3 mb-3 border-b border-gray-200"
        >
          <PatientPrevBtn />
          <span className="ml-2 text-title-medium text-contentColor">
            Save & Exit
          </span>
        </Link>
      </div>
      <div className="full bg-white shadow max-w-[1042px]">
        {/* Progress Bar */}
        <div className="flex flex-col sm:flex-row items-center">
          {/* Step 1 */}
          <div
            className={`flex flex-col items-center gap-2 w-full sm:flex-1 py-4 border-t-[4px] bg-landingBackground  ${
              step > 0 ? "border-darkerBlue" : "border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              {step > 0 ? (
                <ProgressCheck />
              ) : (
                <span className="mr-2">
                  <ProgressBtn />
                </span>
              )}
              <span
                className={`text-body-medium text-contentColor ${
                  step === 0
                    ? ""
                    : step > 0
                    ? ""
                    : "text-coolGrey400"
                }`}
              >
                1. Fill out Challenge Details
              </span>
            </div>
          </div>
          {/* Step 2 */}
          <div
            className={`flex flex-col items-center gap-2 w-full sm:flex-1 py-4  border-t-[4px] bg-landingBackground ${
              step > 1
                ? "border-darkerBlue"
                : step > 1
                ? "bg-landingBackground border-mediumGray"
                : "bg-transparent border-mediumGray"
            }`}
          >
            <div className="flex items-center  h-5">
              {step > 1 ? (
                <ProgressCheck />
              ) : step == 1 ? (
                <span className="mr-2">
                  <ProgressBtn />
                </span>
              ) : (
                <span
                  className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                    step === 1
                      ? "border-2 border-blue-900"
                      : "border text-coolGrey400"
                  }`}
                ></span>
              )}
              <span
                className={`text-body-medium text-contentColor ${
                  step === 1
                    ? ""
                    : step > 1
                    ? ""
                    : "text-coolGrey400"
                }`}
              >
                2. Add Challenge Steps
              </span>
            </div>
          </div>
          {/* Step 3 */}
          <div
            className={`flex flex-col items-center w-full gap-2 sm:flex-1 py-4 border-t-[4px] bg-landingBackground ${
              step > 2
                ? "border-darkerBlue"
                : "bg-transparent border-mediumGray"
            }`}
          >
            <div className="flex items-center h-5">
              <span
                className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                  step === 2
                    ? "border-2 border-blue-900"
                    : "border border-gray-400"
                }`}
              ></span>
              <span
                className={`text-body-medium text-contentColor ${
                  step === 2 ? "" : "text-coolGrey400"
                }`}
              >
                3. Preview Challenge
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full bg-lightBlue min-h-[calc(100vh-400px)]  max-w-[1042px]">
        {step === 0 && (
          <ChallengeDetailsStep
            recurring={recurring}
            setRecurring={setRecurring}
            frequency={frequency}
            setFrequency={setFrequency}
            name={name}
            setName={setName}
            description={description}
            setDescription={setDescription}
            instructor={instructor}
            setInstructor={setInstructor}
            areas={areas}
            setAreas={setAreas}
            media={media}
            setMedia={setMedia}
            handleAreaToggle={handleAreaToggle}
            handleMediaChange={handleMediaChange}
            handleRemoveMedia={handleRemoveMedia}
            onNext={() => setStep(1)}
          />
        )}
        {step === 1 && (
          <ChallengeStepsStep
            lockOrder={lockOrder}
            setLockOrder={setLockOrder}
            stepForm={stepForm}
            setStepForm={setStepForm}
            steps={steps}
            setSteps={setSteps}
            handleStepMediaChange={handleStepMediaChange}
            handleRemoveStepMedia={handleRemoveStepMedia}
            handleAddStep={handleAddStep}
            handleRemoveStep={handleRemoveStep}
            handleAddQuestion={handleAddQuestion}
            VALENCE_ICONS={VALENCE_ICONS}
            TRUE_FALSE_ICONS={TRUE_FALSE_ICONS}
            onBack={() => setStep(0)}
            onNext={() => setStep(2)}
          />
        )}
        {step === 2 && (
          <ChallengePreviewStep
            name={name}
            description={description}
            instructor={instructor}
            areas={areas}
            media={media}
            steps={steps}
            onBack={() => setStep(1)}
            onSave={handleSave}
            activeStep={activeStep}
            onStepAnswersChange={handleStepAnswersChange}
            TRUE_FALSE_ICONS={TRUE_FALSE_ICONS}
            VALENCE_ICONS={VALENCE_ICONS}
          />
        )}
      </div>
    </div>
  );
}
