"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

// Define form validation schema
const forgotPasswordSchema = z.object({
  emailOrPhone: z.string()
    .min(1, { message: "Email or phone is required" })
    .refine((value) => isValidEmailOrPhone(value), {
      message: "Please enter a valid email address or phone number",
    }),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

// Validation function
function isValidEmailOrPhone(value: string): boolean {
  const emailRegex = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/;
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return emailRegex.test(value) || phoneRegex.test(value);
}

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with react-hook-form
  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      emailOrPhone: "",
    },
    mode: "onBlur", // Validate on blur for better user experience
  });

  const handleSubmit = async (data: ForgotPasswordFormValues) => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Redirect to guest page after form submission
      router.push("/forgot-password/guest");
    }, 1500);
  };

  return (
    <div className="flex flex-col">

      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="max-md:h-[450px] md:w-1/2 relative">
          <Image
            src="/auth/forgot-password.svg"
            alt="People in a golf cart"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Forgot Password Form */}
        <div className="w-full md:w-1/2 flex items-center justify-end">
          <div className="w-full max-w-[800px] lg:pr-20 lg:pl-10 max-xl:px-5">
            <div className="max-md:text-center max-md:pt-10">
              <h1 className="text-display-md mb-2 max-md:text-3xl">Forgot your password?</h1>
              <p className="text-darkBlueNormal mb-8 text-body-lg">
                No worries, enter the email address or phone number you used to
                create your account, and we'll send you password reset link.
              </p>
            </div>

            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <div className="mb-10">
                <div className="space-y-2 mt-10">
                  <label htmlFor="emailOrPhone" className="text-body-medium text-paragraphContent">
                    Email or Phone
                  </label>
                  <Input
                    id="emailOrPhone"
                    type="text"
                    {...form.register("emailOrPhone")}
                    className="bg-landingBackground border-0 border-b-2 border-medium-gray rounded-none shadow-none h-12 focus-visible:ring-0 focus-visible:border-gray-500 px-2"
                    disabled={isLoading}
                    placeholder=""
                  />
                  <p className="text-red-500 mt-1">{form.formState.errors.emailOrPhone?.message}</p>
                </div>

                <Button
                  type="submit"
                  variant="netural"
                  className="w-full font-normal h-11 rounded-sm mt-5"
                  disabled={isLoading}
                >
                  <span className="text-white text-title-medium">{isLoading ? "Sending..." : "Send Reset Link"}</span>
                </Button>
              </div>

              <div className="text-sm text-black border-t border-gray-200 py-10">
                No account yet? <Link href="/signup" className="text-black font-medium underline">Sign Up</Link>
              </div>
            </form>
          </div>
        </div>
      </main>
    </div>
  );
}
