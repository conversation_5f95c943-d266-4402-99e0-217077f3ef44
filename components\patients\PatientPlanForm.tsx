import { Button } from "@/components/ui/button";
import Image from "next/image";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { ChevronRight } from "lucide-react";
import { useEffect } from "react";

const cardDetails: Record<string, { number: string; expiry: string; cvv: string }> = {
  card1: {
    number: "4889 9271 1937 1932",
    expiry: "12/28",
    cvv: "***",
  },
  card2: {
    number: "5234 5678 9012 3456",
    expiry: "09/27",
    cvv: "***",
  },
};

export default function PatientPlanForm({
  form,
  setForm,
  errors,
  onBack,
  onNext,
}: any) {
  useEffect(() => {
    if (!form.plan) setForm((f: any) => ({ ...f, plan: "annual" }));
    if (!form.card) setForm((f: any) => ({ ...f, card: "primary" }));
  }, []);
  const selectedCard = cardDetails[form.card] || cardDetails["card1"];
  return (
    <form
      onSubmit={e => {
        e.preventDefault();
        onNext();
      }}
      className="max-w-[431px] mx-auto py-16 font-inter"
    >
      <h2 className="text-lg sm:text-xl font-semibold mb-2">
        Select a patient plan:
      </h2>
      <hr className="mb-4 sm:mb-6 border-gray-200" />
      <div className="flex flex-col sm:flex-row mb-4 gap-2 h-20 sm:mb-6">
        <button
          type="button"
          className={`flex-1 border border-2 rounded-md p-3 pt-2 pr-0 flex flex-col items-start transition-all duration-150 ${
            form.plan === "annual"
              ? "border-primary"
              : "border-[#E6E7EA] "
          }`}
          onClick={() => setForm((f: any) => ({ ...f, plan: "annual" }))}
        >
          <span className="text-body-large font-inter text-pagenationColor mb-1">
            Annual Subscription
          </span>
          <div className="flex items-end">
            <div className="font-inter text-pagenationColor">
              <span className="font-bold text-headline-medium ">
                $116
              </span>
              <span className="font-bold text-body-large">/yr</span>
            </div>
            <span className="ml-4 px-2 py-0.5 text-body-medium rounded-lx bg-landingBackground text-paragraphContent font-semibold">
              SAVE 20%
            </span>
          </div>
        </button>
        <button
          type="button"
          className={`flex-1 border border-2 rounded-md p-3 pt-2 pr-0 flex flex-col items-start transition-all duration-150 ${
            form.plan === "monthly"
              ? "border-primary "
              : "border-[#E6E7EA]"
          }`}
          onClick={() => setForm((f: any) => ({ ...f, plan: "monthly" }))}
        >
          <span className="text-body-large font-inter text-pagenationColor mb-1">
            Monthly Subscription
          </span>
          <div className="flex items-end gap-2">
            <span className=" font-inter text-pagenationColor">
              <span className=" font-bold text-headline-medium">
                $12
              </span>
              <span className=" font-boldtext-body-large">/mo</span>
            </span>
          </div>
        </button>
      </div>
      {errors.plan && (
        <div className="text-red-500 text-sm mb-2">{errors.plan}</div>
      )}
      <label className="block mb-2 text-pagenationColor text-body-large font-bold">
        Select a payment method:
      </label>
      <div className="mb-4">
        <Select
          value={form.card || "primary"}
          onValueChange={(val: string) => setForm((f: any) => ({ ...f, card: val }))}
          required
        >
          <SelectTrigger className={`w-full p-3 border rounded-lg bg-white flex justify-between text-body-large font-bold items-center ${errors.card ? "border-red-500" : "border-gray-200"}`}>
            <SelectValue placeholder="Primary Card" />
          </SelectTrigger>
          <SelectContent className="bg-white text-body-large font-bold text-pagenationColor ">
            <SelectItem className="text-body-large font-bold" value="primary">Primary Card</SelectItem>
            <SelectItem className="text-body-large font-bold" value="card1">Card 1</SelectItem>
            <SelectItem className="text-body-large font-bold" value="card2">Card 2</SelectItem>
          </SelectContent>
        </Select>
        {errors.card && (
          <div className="text-red-500 text-sm mb-2">{errors.card}</div>
        )}
      </div>
      <div className="mb-8">
        <div className="p-3 border rounded-lg bg-[#F8FAFC] shadow-sm">
          <div className="mb-4">
            <Image
              src="/dashboard/card.png"
              alt="Card"
              width={638}
              height={112}
              priority
              className="rounded-xl object-cover w-full"
            />
          </div>
          <div className="px-2 sm:px-4">
            <div className="font-semibold mb-3 text-sm sm:text-[15px]">
              Card Information
            </div>
            <div className="grid grid-cols-2 gap-y-2 gap-x-4 sm:gap-x-6 text-sm sm:text-[15px]">
              {/* Card No. */}
              <div className="flex items-center gap-2">
                <span className="text-gray-600">Card No.</span>
              </div>
              <div className="flex items-center gap-2 justify-end">
                <span className="tracking-wider text-gray-900">
                  {selectedCard.number}
                </span>
              </div>
              {/* Expiry date */}
              <div className="flex items-center gap-2">
                <span className="text-gray-600">Expiry date</span>
              </div>
              <div className="flex items-center gap-2 justify-end">
                <span className="text-gray-900">
                  {selectedCard.expiry}
                </span>
              </div>
              {/* CVV */}
              <div className="flex items-center gap-2">
                <span className="text-gray-600">
                  CVV (3-digit security code)
                </span>
              </div>
              <div className="flex items-center gap-2 justify-end">
                <span className="tracking-widest text-gray-900">
                  {selectedCard.cvv}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <Button
          variant="netural"
          size="sm"
          className="text-lightBlue h-8 w-full sm:w-auto"
          type="button"
          onClick={onBack}
        >
          <span className="text-body-medium">Back</span>
        </Button>
        <Button
          variant="primary"
          size="sm"
          className="text-lightBlue h-8 w-full sm:w-auto"
          type="submit"
        >
          <span className="text-body-medium">Submit Purchase</span>
        </Button>
      </div>
    </form>
  );
}