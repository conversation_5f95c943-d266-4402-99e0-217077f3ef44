"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PlusIcon, ChevronLeft, ChevronRight, PatientUserIcon, ThreeDots } from "@/utils/icons"
import Link from "next/link"
import { Checkbox } from "@/components/ui/checkbox"
import { useRouter } from "next/navigation";

type Patient = {
  id: number;
  name: string;
  subscription: string;
  lastActive: string;
  programs: number;
  challenges: number;
  points: number;
};

const PAGE_SIZE = 10;

function TableSkeleton({ rows = 10 }) {
  return (
    <>
      {Array.from({ length: rows }).map((_, i) => (
        <tr key={i} className="animate-pulse">
          {Array.from({ length: 8 }).map((_, j) => (
            <td key={j} className="px-4 py-3">
              <div className="h-8 bg-gray-200 rounded w-full"></div>
            </td>
          ))}
        </tr>
      ))}
    </>
  );
}

export default function Patients<PERSON><PERSON>() {

  function generateMockPatients(count = 120) {
    const firstNames = [
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
      "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
      "Jacob", "Aria", "Aiden", "Scarlett", "Jackson", "Layla", "Sebastian", "Chloe", "Carter", "Penelope"
    ];
    const lastNames = [
      "James", "Baker", "Miles", "Henkel", "White", "Smith", "Greenberg", "Apple", "Winehouse", "Rogan",
      "Clark", "Brown", "Lee", "Kim", "Turner", "Harris", "Martinez", "Walker", "Robinson", "Scott",
      "Young", "King", "Wright", "Green", "Adams", "Nelson", "Carter", "Perez", "Hall", "Allen",
      "Moore", "Thomas", "Jackson", "Martin", "Thompson", "Garcia", "Martinez", "Roberts", "Lewis", "Walker"
    ];
    const subscriptions = ["Active", "Invited", "Paid"];
    const lastActiveOptions = ["Today", "Yesterday", "N/A", "06/09/23", "06/08/23", "06/07/23", "06/06/23", "06/05/23"];
  
    const patients = [];
    const usedNames = new Set();
    while (patients.length < count) {
      const name = `${firstNames[Math.floor(Math.random() * firstNames.length)]} ${lastNames[Math.floor(Math.random() * lastNames.length)]}`;
      if (usedNames.has(name)) continue;
      usedNames.add(name);
      const subscription = subscriptions[Math.floor(Math.random() * subscriptions.length)];
      const lastActive = subscription === "Invited" ? "N/A" : lastActiveOptions[Math.floor(Math.random() * lastActiveOptions.length)];
      const programs = Math.floor(Math.random() * 4) + 1;
      const challenges = Math.floor(Math.random() * 3);
      const points = subscription === "Invited" ? 0 : Math.floor(Math.random() * 2001) + 500;
      patients.push({ id: patients.length + 1, name, subscription, lastActive, programs, challenges, points });
    }
    return patients;
  }
  
  const [mockPatients, setMockPatients] = useState<Patient[]>([]);
  const [page, setPage] = useState(1);
  const [sortAsc, setSortAsc] = useState(true);
  const [selectedPatients, setSelectedPatients] = useState<Patient[]>([]);
  const router = useRouter();
  useEffect(() => {
    setMockPatients(generateMockPatients(120));
  }, []);

  const totalPages = Math.ceil(mockPatients.length / PAGE_SIZE);

  // Sort patients by name
  const sortedPatients = [...mockPatients].sort((a, b) => {
    if (a.name < b.name) return sortAsc ? -1 : 1;
    if (a.name > b.name) return sortAsc ? 1 : -1;
    return 0;
  });

  const patientsToShow = sortedPatients.slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE);

  // Select all logic
  const allSelected = patientsToShow.length > 0 && patientsToShow.every(p => selectedPatients.some(sel => sel.name === p.name));
  const handleSelectAll = () => {
    if (allSelected) {
      // Deselect all on current page
      setSelectedPatients(selectedPatients.filter(sel => !patientsToShow.some(p => p.name === sel.name)));
    } else {
      // Add all current page patients that aren't already selected
      const newSelected = [...selectedPatients];
      patientsToShow.forEach(p => {
        if (!newSelected.some(sel => sel.name === p.name)) {
          newSelected.push(p);
        }
      });
      setSelectedPatients(newSelected);
    }
  };
  const handleSelectOne = (patient: Patient) => {
    if (selectedPatients.some(sel => sel.name === patient.name)) {
      setSelectedPatients(selectedPatients.filter(sel => sel.name !== patient.name));
    } else {
      setSelectedPatients([...selectedPatients, patient]);
    }
  };

  return (
      <div>
        <div className="flex items-center justify-between mb-6">
          <p className="text-display-md text-paragraphContent">My Patients</p>
          <Link href="/dashboard/patients/new">
            <Button variant="outlinedDark" size="sm" leftIcon={<PlusIcon />}>
              <span className="max-md:hidden">New Patient</span>
            </Button>
          </Link>
        </div>
        <div className="border border-border overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="h-[3em]">
                <td className="px-4 py-3 text-left w-12">
                  <Checkbox checked={allSelected} onCheckedChange={handleSelectAll} />
                </td>
                <td className=" px-4 py-3 text-left cursor-pointer flex gap-3 mt- select-none text-contentColor text-label-large" onClick={() => setSortAsc(s => !s)}>
                  Patient Name <span>{sortAsc ? '↓' : '↑'}</span>
                </td>
                <td className="text-left text-contentColor text-label-large">Subscription</td>
                <td className="text-left text-contentColor text-label-large">Last Active</td>
                <td className="text-left text-contentColor text-label-large">Active Programs</td>
                <td className="text-left text-contentColor text-label-large">Active Challenges</td>
                <td className="text-left text-contentColor text-label-large">Total Points</td>
                <td className=""></td>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {mockPatients.length === 0
                ? <TableSkeleton rows={PAGE_SIZE} />
                : patientsToShow.map((p, i) => (
                    <tr key={i} className="h-[3.5em] bg-lightBlue hover:bg-[#0000000d] cursor-pointer">
                      <td className="px-4 py-3">
                        <Checkbox
                          checked={selectedPatients.some(sel => sel.name === p.name)}
                          onCheckedChange={() => handleSelectOne(p)}
                        />
                      </td>
                      <td className="px-4 py-3 flex items-center gap-2 text-label-large" onClick={() => router.push(`/dashboard?patientId=${p.id}`)}>
                        <span className="inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-gray-400">
                          <PatientUserIcon />
                        </span>
                        <span className="font-medium text-gray-900">{p.name}</span>
                      </td>
                      <td className="text-body-medium text-contentColor">{p.subscription}</td>
                      <td className="text-body-medium text-contentColor">{p.lastActive}</td>
                      <td className="text-body-medium text-contentColor">{p.programs}</td>
                      <td className="text-body-medium text-contentColor">{p.challenges}</td>
                      <td className="text-body-medium text-contentColor">
                        <span className="inline-block bg-gray-100 text-gray-700 rounded-full px-3 py-1 text-xs font-semibold">
                          {p.points.toLocaleString()}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-right">
                        <button className="p-1 rounded hover:bg-gray-100">
                          <ThreeDots />
                        </button>
                      </td>
                    </tr>
                  ))
              }
            </tbody>
          </table>
        </div>
        {/* Pagination with always 7 items */}
        <div className="flex items-center justify-center mt-6 text-gray-600">
          <div className="flex items-center gap-2">
            <button className={`p-2 flex items-center text-title-medium gap-2 ${page === 1 ? 'cursor-not-allowed text-coolGrey60' : 'text-pagenationColor'}`} disabled={page === 1} onClick={() => setPage(page - 1)}><ChevronLeft color={page === 1 ? '#697077' : '#000000'} /> Previous</button>
            {/* Pagination with always 7 items */}
            <div className="max-md:hidden">                
              {(() => {
                const pages = [];
                if (totalPages <= 7) {
                  for (let n = 1; n <= totalPages; n++) {
                    pages.push(n);
                  }
                } else {
                  // Always show 7 items
                  if (page <= 4) {
                    // Show first 5 pages, ellipsis, last
                    pages.push(1, 2, 3, 4, 5, 'end-ellipsis', totalPages);
                  } else if (page >= totalPages - 3) {
                    // Show first, ellipsis, last 5 pages
                    pages.push(1, 'start-ellipsis', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
                  } else {
                    // Show first, ellipsis, 3 pages around current, ellipsis, last
                    pages.push(1, 'start-ellipsis', page - 1, page, page + 1, 'end-ellipsis', totalPages);
                  }
                }
                return pages.map((n, idx) => {
                  if (n === 'start-ellipsis' || n === 'end-ellipsis') {
                    return <span key={n + idx} className="p-3.5 text-title-medium text-pagenationColor font-bold">...</span>;
                  }
                  return (
                    <button
                      key={n}
                      className={`w-10 h-10 ${n === page ? 'bg-pagenationHoverColor text-darkerBlue text-title-medium' : 'hover:bg-pagenationHoverColor text-pagenationColor'}`}
                      onClick={() => setPage(n as number)}
                    >{n}</button>
                  );
                });
              })()}
            </div>
            <button className={`p-2 flex items-center text-title-medium gap-2 ${page === totalPages ? 'cursor-not-allowed text-coolGrey60' : 'text-pagenationColor'}`} disabled={page === totalPages} onClick={() => setPage(page + 1)}>Next <ChevronRight color={page === totalPages ? '#697077' : '#000000'} /></button>
          </div>
        </div>
      </div>
  )
} 