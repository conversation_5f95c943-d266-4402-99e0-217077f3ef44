'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface PatientDialogContextType {
  isDialogOpen: boolean;
  openDialog: () => void;
  closeDialog: () => void;
  showQuiz: boolean;
  setShowQuiz: (show: boolean) => void;
}

const PatientDialogContext = createContext<PatientDialogContextType | undefined>(undefined);

export function PatientDialogProvider({ children }: { children: ReactNode }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showQuiz, setShowQuiz] = useState(false);

  const openDialog = () => {
    setIsDialogOpen(true);
    setShowQuiz(true); // Open directly to quiz when retaking
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setShowQuiz(false);
  };

  return (
    <PatientDialogContext.Provider
      value={{
        isDialogOpen,
        openDialog,
        closeDialog,
        showQuiz,
        setShowQuiz,
      }}
    >
      {children}
    </PatientDialogContext.Provider>
  );
}

export function usePatientDialog() {
  const context = useContext(PatientDialogContext);
  if (context === undefined) {
    throw new Error('usePatientDialog must be used within a PatientDialogProvider');
  }
  return context;
}
