"use client"

import { <PERSON>actN<PERSON>, useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname} from "next/navigation"
import { AlignJustify } from "lucide-react"
import {
  LogOut,
  Bell
} from "lucide-react"
import {UserIcon, ChartPie, Program, Ebook, Account, Challenge} from "@/utils/icons";
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { PatientDialog } from './PatientDialog';

interface DashboardLayoutProps {
  children: ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const pathname = usePathname()
  const userRole = useSelector((state: RootState) => state.user.user?.role);

  const patientSidebarItems = [
    {
      icon: ChartPie,
      label: "My Dashboard",
      href: "/dashboard",
    },
    {
      icon: Program,
      label: "My Programs",
      href: "/dashboard/programs",
    },
    {
      icon: Ebook,
      label: "My Challenges",
      href: "/dashboard/challenges",
    },
    {
      icon: Account,
      label: "My Account",
      href: "/dashboard/account",
    },
  ]

  const providerSidebarItems = [
    {
      icon: ChartPie,
      label: "My Dashboard",
      href: "/dashboard",
    },
    {
      icon: Program,
      label: "My Programs",
      href: "/dashboard/programs",
    },
    {
      icon: Ebook,
      label: "My Challenges",
      href: "/dashboard/challenges",
    },
    {
      icon: Challenge,
      label: "My Patients",
      href: "/dashboard/patients",
    },
    {
      icon: Account,
      label: "My Account",
      href: "/dashboard/account",
    },
  ]

  const sidebarItems = userRole === 'patient' ? patientSidebarItems : providerSidebarItems

  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex flex-col md:flex-row h-screen bg-[#f5f7fa]">
      {/* Mobile Header */}
      <div className="md:hidden bg-white p-4 flex items-center justify-between shadow-sm">
        <Link href="/dashboard">
          <Image
            src="/logo.svg"
            alt="Peakality Logo"
            width={120}
            height={30}
          />
        </Link>
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className="p-2 rounded-md text-gray-500 hover:bg-gray-100"
        >
          {sidebarOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <AlignJustify />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <aside className={`
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        md:translate-x-0
        transition-transform duration-300 ease-in-out
        fixed md:static
        top-0 left-0
        h-full z-40
        w-64 bg-white shadow-md
        md:h-screen
      `}>
        <div className="p-4 hidden md:block mt-5">
          <Link href="/dashboard">
            <Image
              src="/logo.svg"
              alt="Peakality Logo"
              width={200}
              height={100}
              className="mx-auto"
            />
          </Link>
        </div>

        {/* User Profile */}
        <div className="px-5 py-4">
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full bg-landingBackground flex items-center justify-center">
              <UserIcon />
            </div>
            <div className="ml-3 font-normal text-black">
              <p className="text-title-lg">{userRole === 'patient' ? 'Sarah James' : 'Liz Renda'}</p>
              <p className="text-body-small">@{userRole === 'patient' ? 'sarahthepatient' : 'lizthetherapist'}</p>
            </div>
            <div className="ml-auto relative mr-3">
              <Bell className="h-5 w-5 text-gray-500" />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {userRole === 'patient' ? '9' : '1'}
              </span>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="px-4 py-1">
          <ul>
            {sidebarItems.map((item, index) => {
              const isActive = pathname === item.href
              return (
                <li key={index}>
                  <Link
                    href={item.href}
                    className={`flex items-center px-4 py-3 text-title-medium ${
                      isActive
                        ? "bg-[#F0F3F6]"
                        : "hover:bg-landingBackground"
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <div className="flex items-center w-7 h-7"><item.icon /></div>
                    <div className="ml-1">{item.label}</div>
                  </Link>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* Logout */}
        <div className="absolute bottom-0 w-64 border-t p-4">
          <Link
            href="/login"
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <LogOut className="h-5 w-5 mr-3" />
            <span>Log out</span>
          </Link>
        </div>
      </aside>

      {/* Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      {/* Main Content */}
      <main className="flex-1 overflow-y-auto p-8 max-md:p-4 bg-landingBackground">
        {userRole === 'patient' && <PatientDialog />}
        {children}
      </main>
    </div>
  )
}
