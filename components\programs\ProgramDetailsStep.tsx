"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Image from "next/image";
import { PreviewNewProgram, UploadIcon } from "@/utils/icons";
import { Button } from "@/components/ui/button";
import {ALLOWED_IMAGE_TYPES, ALLOWED_VIDEO_TYPES} from "@/lib/types";

const ALLOWED_TYPES = [...ALLOWED_IMAGE_TYPES, ...ALLOWED_VIDEO_TYPES];

export default function ProgramDetailsStep({
  name,
  setName,
  description,
  setDescription,
  onNext,
  media,
  setMedia,
  onFileChange,
}: any) {
  const [mounted, setMounted] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string>("");
  const [mediaPreview, setMediaPreview] = useState<string>("");
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    description?: string;
    media?: string;
  }>({});

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const convertBlobToFile = async () => {
      if (media && media.startsWith("blob:")) {
        try {
          const response = await fetch(media);
          const blob = await response.blob();
          const file = new File([blob], "media-file", { type: blob.type });
          setFile(file);
          setMediaPreview(media);
          if (onFileChange) {
            onFileChange(file);
          }
        } catch (error) {
          console.error("Error converting blob to file:", error);
          setMediaPreview("");
          setFile(null);
        }
      } else if (media) {
        setMediaPreview(media);
      }
    };

    convertBlobToFile();
  }, [media, onFileChange]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError("");
    if (e.target.files && e.target.files[0]) {
      const selected = e.target.files[0];

      if (!ALLOWED_TYPES.includes(selected.type)) {
        setError(
          "Only image and video files (jpg, png, gif, mp4, webm, mov, etc.) are allowed."
        );
        setFile(null);
        setMediaPreview("");
        setMedia("");
        if (onFileChange) onFileChange(null);
        return;
      }

      if (selected.size > 200 * 1024 * 1024) {
        setError("File size must be less than 2MB");
        setFile(null);
        setMediaPreview("");
        setMedia("");
        if (onFileChange) onFileChange(null);
        return;
      }

      setFile(selected);
      const url = URL.createObjectURL(selected);
      setMediaPreview(url);
      setMedia(url);

      if (onFileChange) {
        onFileChange(selected);
      }
    }
  };

  const handleRemove = () => {
    if (mediaPreview && mediaPreview.startsWith("blob:")) {
      URL.revokeObjectURL(mediaPreview);
    }

    setFile(null);
    setMediaPreview("");
    setMedia("");
    setError("");

    // Media removal handled locally
  };

  const renderPreview = () => {
    if (!mediaPreview) return <PreviewNewProgram />;

    if (
      (file && file.type.startsWith("image/")) ||
      (!file &&
        mediaPreview.match(/^data:image|\.jpg|\.jpeg|\.png|\.gif|\.webp$/))
    ) {
      return (
        <Image
          src={mediaPreview}
          alt="Preview"
          width={88}
          height={65}
          className="rounded object-cover"
        />
      );
    }

    if (
      (file && file.type.startsWith("video/")) ||
      (!file &&
        mediaPreview.match(/^data:video|\.mp4|\.webm|\.ogg|\.mov|\.quicktime$/))
    ) {
      return (
        <video
          src={mediaPreview}
          width={88}
          height={65}
          className="w-full h-full object-cover rounded"
          controls
          preload="metadata"
        />
      );
    }

    return null;
  };

  const validateForm = () => {
    const errors: {
      name?: string;
      description?: string;
      media?: string;
    } = {};

    if (!name.trim()) {
      errors.name = "Challenge name is required";
    }

    if (!description.trim()) {
      errors.description = "Challenge description is required";
    } else if (description.length < 10) {
      errors.description = "Description must be at least 10 characters long";
    }

    if (!mediaPreview && !file) {
      errors.media = "Please upload a photo or video";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  if (!mounted) {
    return null; // or a loading skeleton
  }

  return (
    <div className="flex justify-center w-full">
      <div className="px-2 py-4 sm:p-8 w-full max-w-full sm:max-w-[650px] mt-2">
        <div className="text-body-lg font-bold mb-6 border-b-2 border-border pb-2">
          Fill out Challenge Details:
        </div>
        <div className="mb-6">
          <div className="relative mb-6">
            <label className="text-paragraphContent text-title-small font-bold mb-10">Program Name</label>
            <Input
              className={`mb-1 bg-landingBackground w-full ${
                validationErrors.name ? "border-red-500" : ""
              }`}
              placeholder="Name the challenge..."
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
            {validationErrors.name && (
              <p className="text-red-500 text-sm mb-4">
                {validationErrors.name}
              </p>
            )}
          </div>
          <div className="relative mb-6">
            <label className="text-paragraphContent text-title-small font-bold mb-2">Program Name</label>
            <Textarea
              rows={5}
              className={`mb-1 bg-landingBackground w-full ${
                validationErrors.description ? "border-red-500" : ""
              }`}
              placeholder="Describe the challenge..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
            {validationErrors.description && (
              <p className="text-red-500 text-sm mb-4">
                {validationErrors.description}
              </p>
            )}
          </div>
        </div>
        <div className="mt-7 pb-4 mb-8 border-b border-gray-200">
          <div className="flex flex-col">
            <label className="text-body-medium text-paragraphContent font-bold mb-2">
              Choose a Photo or Video
            </label>
            <div className="flex flex-col md:flex-row w-full gap-4 max-md:gap-0">
              <div className="flex w-full md:w-auto">
                <div className="flex justify-center items-center mb-2 md:mb-0 md:mr-4 w-full md:w-24 h-[120px] md:h-24 overflow-hidden">
                  {renderPreview()}
                </div>
                <div className="flex flex-col justify-center w-full md:w-auto">
                  <input
                    type="file"
                    accept="image/*,video/*"
                    className="hidden"
                    id="file-upload"
                    onChange={handleFileChange}
                  />
                  <div className="flex flex-col justify-center items-center">
                    <label
                      htmlFor="file-upload"
                      className="flex items-center gap-2 border border-darkBlueNormal text-body-medium rounded px-4 h-9 text-darkBlueNormal bg-lightBlue cursor-pointer hover:bg-gray-50"
                    >
                      <UploadIcon />
                      Upload
                    </label>
                    <a
                      className={`text-body-medium mt-2 cursor-pointer ${
                        !mediaPreview
                          ? "text-[#AAAAAA]"
                          : "text-paragraphContent"
                      }`}
                      onClick={handleRemove}
                    >
                      remove
                    </a>
                  </div>
                </div>
              </div>

              <div className="hidden md:block w-px bg-darkBlueBackground h-18" />
              <div className="block md:hidden border-t border-gray-200" />
              <div className="flex flex-col md:justify-around gap-2 w-full mt-4 md:mt-0 ml-4">
                <div className="text-paragraphContent flex flex-col justify-center text-body-medium ">
                  <div className="mb-2">Image requirements:</div>
                  <div className="ml-1">1. Min. 400 x 400px</div>
                  <div className="ml-1">2. Max. 2MB</div>
                </div>
              </div>
            </div>
          </div>
          {(error || validationErrors.media) && (
            <div className="text-red-500 text-sm text-center mt-2">
              {error || validationErrors.media}
            </div>
          )}
        </div>
        <div className="flex justify-end">
          <Button
            variant="netural"
            size="sm"
            className="text-lightBlue h-8 w-full sm:w-auto px-6"
            onClick={handleNext}
          >
            <span className="text-body-medium">Next</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
