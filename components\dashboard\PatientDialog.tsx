// components/dashboard/PatientDialog.tsx
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { QuizContent } from './QuizContent';
import { usePatientDialog } from '@/contexts/PatientDialogContext';

export function PatientDialog() {
    const { isDialogOpen, closeDialog, showQuiz, setShowQuiz } = usePatientDialog();

    const handleBackToInitial = () => {
        setShowQuiz(false);
    };

    return (
        <Dialog open={isDialogOpen} onOpenChange={closeDialog}>
            <DialogContent className="p-0 overflow-hidden max-w-[1000px] w-[90vw]">
                {!showQuiz ? (
                    // Initial Dialog Content
                    <div className="w-full">
                         <div className="relative w-full h-32 md:h-[350px]">
                            <Image
                                src="/dashboard/travel.jpg"
                                alt="Dialog Header Image"
                                fill
                                className="w-full h-full object-cover"
                                priority
                            />
                        </div>

                        <div className="w-full mt-5 md:mt-10 text-paragraphContent max-md:p-3">
                            <DialogHeader>
                                <DialogTitle className="text-center mx-auto">
                                    <span className="text-2xl md:text-headline-large font-bold mb-4">Hi, Sarah! How is your Wheel of Life traveling?</span>
                                </DialogTitle>
                                <DialogDescription className="text-center mb-6 mx-auto max-w-[800px]">
                                    <span className="text-sm md:text-body-lg">The Wheel of Life is a visual representation of one's satisfaction with their own life. It's often used in spiritual, philosophical, coaching and therapy traditions to breakdown the different areas of a person's life into bite size sections. It illustrates the balance between these areas and what areas would cause you to have a "flat tire" while riding down your journey of life.</span>
                                </DialogDescription>
                            </DialogHeader>

                            <div className="flex justify-center py-5 md:py-10">
                                <Button
                                    variant="primary"
                                    onClick={() => setShowQuiz(true)}
                                >
                                    Take the Wheel of Life Quiz
                                </Button>
                            </div>
                        </div>
                    </div>
                ) : (
                    // Quiz Content
                    <QuizContent onBack={handleBackToInitial} />
                )}
            </DialogContent>
        </Dialog>
    );
}