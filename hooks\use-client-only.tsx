"use client"

import { useState, useEffect } from 'react'

/**
 * A hook that returns a value only on the client side
 * @param initialValue The initial value to use on the server
 * @param clientFn A function that returns the value to use on the client
 * @returns The value from clientFn on the client, or initialValue on the server
 */
export function useClientOnly<T>(initialValue: T, clientFn: () => T): T {
  const [value, setValue] = useState<T>(initialValue)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    setValue(clientFn())
  }, [clientFn])

  return isClient ? value : initialValue
}

/**
 * A hook that safely accesses window properties
 * @returns An object with safe window access methods
 */
export function useWindowSafe() {
  const isClient = useClientOnly(false, () => true)

  return {
    /**
     * Safely get window width
     */
    width: useClientOnly(1024, () => window.innerWidth),
    
    /**
     * Safely get window height
     */
    height: useClientOnly(768, () => window.innerHeight),
    
    /**
     * Safely get window scroll position
     */
    scrollY: useClientOnly(0, () => window.scrollY),
    
    /**
     * Safely check if window is available
     */
    isClient,
    
    /**
     * Safely scroll to a position
     */
    scrollTo: (options: ScrollToOptions) => {
      if (isClient) {
        window.scrollTo(options)
      }
    }
  }
}
