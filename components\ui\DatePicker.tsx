"use client";
import * as React from "react";
import TextField from "@mui/material/TextField";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker as MUIDatePicker } from "@mui/x-date-pickers/DatePicker";
import type { Dayjs } from "dayjs";

interface DatePickerProps {
  value: Dayjs | null;
  onChange: (value: Dayjs | null) => void;
  error?: boolean;
  helperText?: string;
  required?: boolean;
  fullWidth?: boolean;
  disabled?: boolean;
  label?: string;
}

export function DatePicker({
  value,
  onChange,
  error = false,
  helperText = "",
  required = false,
  fullWidth = true,
  disabled = false,
  label = "Date",
}: DatePickerProps) {
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <MUIDatePicker
        value={value}
        onChange={onChange}
        disabled={disabled}
        className="bg-landingBackground"
        slotProps={{
          textField: {
            fullWidth,
            required,
            error,
            variant: "filled",
            InputLabelProps: { shrink: true },
            sx: {
              
              '& .css-dwgfb1-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersFilledInput-sectionsContainer': {
                display: 'flex',
                alignItems: 'center',
                padding: '14px 20px 11px 20px',
              },
              '& .css-1fl2pvs-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer-MuiPickersFilledInput-sectionsContainer': {
                display: 'flex',
                alignItems: 'center',
                padding: '14px 20px 11px 20px',
                textTransform: 'lowercase',
              },
              '& .MuiPickersFilledInput-root': {
                background: "#F2F4F8",                
                textTransform: 'lowercase',
              },
              '& .MuiPickersFilledInput-root:hover': {
                background: "#F2F4F8",
              },
              '& .MuiPickersFilledInput-root:focus': {
                background: "#F2F4F8",
              }
            },
          },
        }}
        format="MM/DD/YYYY"
      />
    </LocalizationProvider>
  );
}

export default DatePicker; 