"use client";

import Link from "next/link";
import Image from "next/image";
import { ArrowRight } from "lucide-react";
import { Provider, Individual } from "@/utils/icons"
import { useRouter } from "next/navigation";

export default function SignupPage() {
  const router = useRouter();

  const handleProviderClick = () => {
    router.push("/signup/provider");
  };

  const handleIndividualClick = () => {
    router.push("/signup/individual");
  };
  return (
    <div className="flex flex-col">

      <main className="flex max-md:flex-col container-fluid md:h-[calc(100vh-81px)]">
        {/* Left side - Image */}
        <div className="md:w-1/2 max-md:h-[450px] relative">
          <Image
            src="/auth/signup.svg"
            alt="Mountain landscape"
            fill
            className="w-full h-full max-md:object-cover"
            priority
          />
        </div>

        {/* Right side - Sign Up options */}
        <div className="w-full md:w-1/2 md:pl-10 flex items-center max-md:justify-center">
          <div className="w-full max-w-[500px] px-8 max-md:py-10">
            <h1 className="text-display-md mb-10 max-md:text-center">Sign Up</h1>

            <p className="text-body-lg mb-8">
              To begin this journey, tell us what type of account you'd be opening.
            </p>

            {/* Account type options */}
            <div className="space-y-4">
              {/* Individual account option */}
              <div
                className="rounded-md p-5 shadow-md hover:shadow-lg transition-colors cursor-pointer group"
                onClick={handleIndividualClick}
              >
                <div className="flex items-center justify-between">
                  <div className="flex gap-5">
                    <Individual />
                    <div className="flex flex-col">
                      <h3 className="text-title-medium">Individual</h3>
                      <div className="max-w-[220px]">
                        <p className="text-body-medium text-darkBlueNormal">Personal account to manage your challenges and progress.</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <ArrowRight className="h-5 w-5 group-hover:text-gray-600" />
                  </div>
                </div>
              </div>

              {/* Provider account option */}
              <div
                className="rounded-md p-5 shadow-md hover:shadow-lg transition-colors cursor-pointer group mt-20"
                onClick={handleProviderClick}
              >
                <div className="flex items-center justify-between">
                  <div className="flex gap-5">
                    <Provider />
                    <div className="flex flex-col">
                      <h3 className="text-title-medium">Provider</h3>
                      <div className="max-w-[220px]">
                        <p className="text-body-medium text-darkBlueNormal">Are you a provider of clinical patients? This account is for you!</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <ArrowRight className="h-5 w-5 group-hover:text-gray-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Login link */}
            <div className="mt-16 text-sm text-black border-t border-gray-200 pt-12">
              Already have an account? <Link href="/login" className="font-medium underline">Log In</Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
