"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, UserRound, Plus } from 'lucide-react'
import Link from 'next/link'
import {FilledUserIcon, GreenCheck, RedCheck, LightRedCheck, BrownCheck, BlueCheck, DarkBlueCheck, YellowCheck, PurpleCheck, DarkGreenChe<PERSON>, SixD<PERSON> } from '@/utils/icons';

interface Instructor {
  name: string
  courses: number
}

interface ProgramDetailProps {
  id: number
  title: string
  image?: string
  challenges: number
  duration: number
  reward: number
  description: string
  instructor?: Instructor
}

export function ProgramDetail({
  id,
  title,
  image,
  challenges,
  duration,
  reward,
  description,
  instructor
}: ProgramDetailProps) {
  return (
    <div>
      {/* Header */}
      <div className="px-1 border-b border-gray-200">
        <div className="mx-auto">
        <h1 className="text-3xl sm:text-display-md text-paragraphContent font-bold mb-4 sm:mb-8">My Programs</h1>    
          <div className="flex items-center pb-2 text-paragraphContent text-title-medium">
            <Link href="/dashboard/programs" className="flex items-center text-paragraphContent">
              <ArrowLeft size={16} className="mr-3" />
              <span>Back to All Programs</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-2 py-4 md:px-4 grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
        {/* Left Column - Program List */}
        <div className="md:col-span-2 space-y-6">
          {/* Program Card */}
          <div className="bg-white rounded-md shadow-sm border border-gray-200 overflow-hidden py-2">
            {/* Card Header */}
            <div className="flex justify-between items-center pt-3 px-4">
              <h3 className="text-paragraphContent text-title-lg font-bold">
                {id}. {title}
              </h3>
              <button className="text-gray-500 hover:text-gray-700">
                <Ellipsis size={16} />
              </button>
            </div>

            {/* Areas of Life */}
            <div className="flex items-center px-4 py-2">
              <div className="text-paragraphContent text-body-small">Areas of Life:</div>
              <div className="flex -space-x-1 flex-wrap ml-2 items-center">
                <div className='border border-border rounded-full p-1 bg-landingBackground'><GreenCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><RedCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><LightRedCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BrownCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkBlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><YellowCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><PurpleCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkGreenCheck /></div>
              </div>
            </div>

            {/* Card Image */}
            {image && (
              <div className="mb-2 px-4">
                <div className="w-full h-64 relative">
                  <img
                    src={image}
                    alt={title}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            )}

            {/* Card Info */}
            <div className="px-4 py-2 flex justify-between text-paragraphContent text-body-small">
              <div className="flex space-x-4">
                <div>
                  <span className="font-bold">Steps:</span> {challenges}
                </div>
                <div>
                  <span className="font-bold">Duration:</span> {duration} Days
                </div>
                <div>
                  <span className="font-bold">Reward:</span> {reward} Points
                </div>
              </div>
            </div>

            {/* User Avatars and Details Button */}
            <div className="px-4 py-2 flex justify-between items-center">
              <div className="flex -space-x-2">
                {[...Array(5)].map((_, index) => (
                  <div
                    key={index}
                    className="w-7 h-7 rounded-full bg-landingBackground border border-border flex items-center justify-center"
                  >
                    <FilledUserIcon />
                  </div>
                ))}
                <div className="w-7 h-7 rounded-full bg-landingBackground border border-border flex items-center justify-center">
                  <Plus fill="#1D242D" size={12} />
                </div>
              </div>
              <button className="text-xs bg-[#4a6380] hover:bg-[#3a5270] text-white px-4 py-2 rounded">
                Details
              </button>
            </div>
          </div>

          {/* Additional Program Cards can be added here */}
        </div>

        {/* Right Column - Program Details */}
        <div className="md:col-span-1 max-w-[400px]">
          <div className="bg-white rounded-md shadow-sm border border-gray-200 overflow-hidden p-1">
            {/* Card Header */}
            <div className="flex justify-between items-center px-4 py-3">
              <h3 className="text-headline-small text-paragraphContent font-bold">
                Welcome to Peakality
              </h3>
              <button className="text-gray-500 hover:text-gray-700">
                <Ellipsis size={16} />
              </button>
            </div>

            {/* Areas of Life */}
            <div className="flex items-center px-4 pb-2">
              <div className="text-body-small text-paragraphContent">Areas of Life:</div>
              <div className="flex -space-x-1 flex-wrap items-center ml-2">
                <div className='border border-border rounded-full p-1 bg-landingBackground'><GreenCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><RedCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><LightRedCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BrownCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkBlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><YellowCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><PurpleCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkGreenCheck /></div>
              </div>
            </div>

            {/* Card Image */}
            {image && (
              <div className="px-4 mb-2">
                <div className="w-full h-62 relative">
                  <img
                    src={image}
                    alt={title}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            )}

            {/* Card Info */}
            <div className="px-4 py-2 flex justify-between text-paragraphContent text-body-small">
              <div className="flex space-x-4">
                <div>
                  <span className="font-bold">Challenges:</span> {challenges}
                </div>
                <div>
                  <span className="font-bold">Duration:</span> {duration} Days
                </div>
              </div>
              <div>
                <span className="font-bold">Reward:</span> {reward} Points
              </div>
            </div>

            {/* User Avatars */}
            <div className="px-3 py-2 flex border-t border-gray-100">
              <div className="flex -space-x-2">
                {[...Array(5)].map((_, index) => (
                  <div
                    key={index}
                    className="w-7 h-7 rounded-full bg-landingBackground border border-border flex items-center justify-center"
                  >
                    <FilledUserIcon />
                  </div>
                ))}
                <div className="w-7 h-7 rounded-full bg-landingBackground border border-border flex items-center justify-center">
                  <Plus fill="#1D242D" size={12} />
                </div>
              </div>
            </div>

            {/* About the Program */}
            <div className="px-3 pt-2 text-body-small text-paragraphContent">
              <h4 className="font-bold mb-2">About the Program:</h4>
              <p className="mb-4">{description}</p>
            </div>

            {/* About the Instructor */}
            {instructor && (
              <div className="px-3 py-2">
                <h4 className="text-body-small font-bold mb-2">About the Instructor:</h4>
                <div className="flex items-center mb-2 text-paragraphContent">
                  <div className="w-6 h-6 rounded-full bg-landingBackground border border-border flex items-center justify-center mr-2">
                    <FilledUserIcon />
                  </div>
                  <span className="text-body-small font-bold">{instructor.name}</span>
                  <span className="text-body-small text-paragraphContent ml-auto">Courses: {instructor.courses}</span>
                </div>
                <p className="text-body-small text-paragraphContent">
                  Vestibulum augue prom enim cursus morties ante. Ultricies posuere mattis elit egestas aliquam pellentesque nisi et dui. Fusce vulputate tincidunt vulputat lectus quam felis nisi augue egestas.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
