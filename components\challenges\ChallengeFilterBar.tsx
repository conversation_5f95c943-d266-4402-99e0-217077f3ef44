interface ChallengeFilterBarProps {
  areas: string[];
  selectedArea: string;
  onSelect: (area: string) => void;
}

export function ChallengeFilterBar({ areas, selectedArea, onSelect }: ChallengeFilterBarProps) {
  return (
    <div className="flex flex-wrap gap-4 mb-4 border-b">
      {areas.map((area) => (
        <button
          key={area}
          className={` p-3 text-title-medium transition-colors duration-150 ${
            selectedArea === area
              ? "border-b-2 border-oldBlue"
              : "text-darkBlueNormal"
          }`}
          onClick={() => onSelect(area)}
        >
          {area}
        </button>
      ))}
    </div>
  );
} 