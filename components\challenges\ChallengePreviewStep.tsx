"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import { AreaIcon } from "@/components/dashboard/AreaIcon";
import clsx from "clsx";
import { PassedIcon, ReviewIcon, LockIcon, UserAvatar } from "@/utils/icons";
import { Button } from "@/components/ui/button";

interface ChallengePreviewStepProps {
  name: string;
  description: string;
  instructor: string;
  areas: string[];
  media: string;
  steps: any[];
  onBack: () => void;
  onSave: () => void;
  activeStep: number;
  onStepAnswersChange: (steps: any[]) => void;
  TRUE_FALSE_ICONS: string[];
  VALENCE_ICONS: string[];
}

export default function ChallengePreviewStep({
  name,
  description,
  instructor,
  areas,
  media,
  steps,
  onBack,
  onSave,
  activeStep: propActiveStep,
  onStepAnswersChange,
  TRUE_FALSE_ICONS,
  VALENCE_ICONS,
}: ChallengePreviewStepProps) {
  // Find the first uncompleted step
  const getFirstUncompletedStep = () => {
    for (let i = 0; i < steps.length; i++) {
      if (
        steps[i].addQuestionnaire &&
        steps[i].questions.length > 0 &&
        !areAllQuestionsAnswered(i)
      ) {
        return i;
      }
    }
    return steps.length; // All completed
  };

  // Helper: are all questions in a step answered?
  function areAllQuestionsAnswered(stepIdx: number) {
    if (stepIdx < 0 || stepIdx >= steps.length) return true;
    const step = steps[stepIdx];
    console.log(step);
    if (!step.addQuestionnaire || !step.questions.length) return true;
    return step.questions.every((q: { response?: any }) => q.response !== undefined && q.response !== null && q.response !== "");
  }

  const [activeStep, setActiveStep] = useState<number>(
    propActiveStep ?? getFirstUncompletedStep()
  );

  // Keep local activeStep in sync with parent if parent changes it
  useEffect(() => {
    setActiveStep(propActiveStep ?? getFirstUncompletedStep());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [propActiveStep, steps.length]);

  // Handler for answer selection
  const handleAnswerSelect = (
    stepIdx: number,
    questionIdx: number,
    answerIdx: number
  ) => {
    if (stepIdx === activeStep) {
      const updatedSteps = steps.map((step, sIdx) => {
        if (sIdx === stepIdx) {
          return {
            ...step,
            questions: step.questions.map((q: any, qIdx: number) => {
              if (qIdx === questionIdx) {
                return { ...q, response: answerIdx };
              }
              return q;
            }),
          };
        }
        return step;
      });
      onStepAnswersChange(updatedSteps);
    }
  };

  // Handler for Start/View button
  const handleStepAction = (idx: number, isStart: boolean) => {
    // Check if trying to move forward
    if (idx > activeStep) {
      // Verify current step is complete
      const currentStep = steps[activeStep];
      if (currentStep.addQuestionnaire && currentStep.questions) {
        const isCurrentStepComplete = currentStep.questions.every((q: { response?: number }) => q.response !== undefined && q.response !== null);
        if (!isCurrentStepComplete) {
          return; // Prevent moving forward if current step is not complete
        }
      }
    }
    setActiveStep(idx);
  };

  // Progress calculation
  const completedSteps = steps.filter((_: any, idx: number) =>
    areAllQuestionsAnswered(idx)
  ).length;
  const totalSteps = steps.length;
  const progressPercent =
    totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

  const [mainMediaType, setMainMediaType] = useState<string>("unknown");

  function getMediaType(url: string) {
    if (!url) return "unknown";
    // Check for blob or data URLs (video/image)
    if (url.startsWith("blob:") || url.startsWith("data:")) {
      // Try to guess from the data URL
      if (url.startsWith("data:video") || url.includes("video/"))
        return "video";
      if (url.startsWith("data:image") || url.includes("image/"))
        return "image";
      if (url.startsWith("data:application/pdf") || url.includes("pdf"))
        return "pdf";
      return "unknown";
    }
    // Check file extension
    if (url.match(/\.(mp4|webm|ogg|mov|quicktime)$/i)) return "video";
    if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) return "image";
    if (url.match(/\.(pdf)$/i)) return "pdf";
    return "unknown";
  }

  useEffect(() => {
    async function detectType() {
      if (!media) {
        setMainMediaType("unknown");
        return;
      }
      if (media.startsWith("blob:")) {
        try {
          const response = await fetch(media);
          const blob = await response.blob();
          if (blob.type.startsWith("image/")) setMainMediaType("image");
          else if (blob.type.startsWith("video/")) setMainMediaType("video");
          else if (blob.type === "application/pdf") setMainMediaType("pdf");
          else setMainMediaType("unknown");
        } catch {
          setMainMediaType("unknown");
        }
      } else {
        // Fallback to extension check for remote URLs or data URLs
        if (media.match(/\.(mp4|webm|ogg|mov|quicktime)$/i))
          setMainMediaType("video");
        else if (media.match(/\.(jpg|jpeg|png|gif|webp)$/i))
          setMainMediaType("image");
        else if (media.match(/\.(pdf)$/i)) setMainMediaType("pdf");
        else if (media.startsWith("data:video")) setMainMediaType("video");
        else if (media.startsWith("data:image")) setMainMediaType("image");
        else setMainMediaType("unknown");
      }
    }
    detectType();
  }, [media]);

  const renderMedia = () => {
    if (!media) return null;
    if (mainMediaType === "image") {
      return (
        <Image
          src={media}
          alt="Main"
          width={60}
          height={60}
          className="object-cover w-full mb-6"
        />
      );
    }
    if (mainMediaType === "video") {
      return (
        <video
          src={media}
          controls
          className="w-full h-auto rounded mb-6 object-cover"
        />
      );
    }
    if (mainMediaType === "pdf") {
      return (
        <iframe
          src={media}
          className="w-full h-auto rounded mb-6"
          title="Document preview"
        />
      );
    }
    return (
      <a
        href={media}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 underline mb-6 block"
      >
        Open Document
      </a>
    );
  };


  return (
    <div className="p-4">
      <div className="text-body-lg font-bold text-paragraphContent mb-4 pb-4 border-b border-gray-200">
        Preview Challenge:
      </div>
      <div className="flex flex-col md:flex-row gap-8 mx-auto">
        {/* Main Content */}
        <div className="flex-1 min-w-0">
          <div className="max-md:hidden max-w-[678px] mx-auto">
            {renderMedia()}
          </div>
          <div className="flex flex-col gap-3">
            {steps.map((s: any, idx: number) => {
              const isActive = idx === activeStep;
              const isCompleted = areAllQuestionsAnswered(idx);
              const prevAnswered =
                idx === 0 || areAllQuestionsAnswered(idx - 1);
              const showStartButton = !isCompleted && idx !== activeStep;
              const startButtonDisabled = !prevAnswered;

              return (
                <Card
                  key={idx}
                  className={clsx(
                    "px-2 py-3 flex flex-col justify-between items-center border rounded transition-all w-full"
                  )}
                >
                  <div className="flex flex-col sm:flex-row w-full justify-between gap-2">
                    <div className="flex gap-2 justify-center items-center w-full sm:w-auto">
                      <div className="w-6 h-6 flex-shrink-0 rounded-full flex bg-landingBackground justify-center items-center">
                        {isCompleted ? (
                          <PassedIcon />
                        ) : isActive ? (
                          <ReviewIcon />
                        ) : (
                          <LockIcon />
                        )}
                      </div>
                      <span
                        className={clsx(
                          "font-bold text-body-large text-paragraphContent max-md:max-w-full truncate text-base",
                          isActive ? "max-w-[300px]" : "max-w-[200px]"
                        )}
                      >
                        {idx + 1}. {s.name}
                      </span>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 items-center w-full sm:w-auto mt-2 sm:mt-0">
                      <div className="flex gap-2 text-body-small text-paragraphContent w-full sm:w-auto">
                        <span>
                          <span className="font-semibold">Difficulty:</span>{" "}
                          {s.difficulty}
                        </span>
                        <span>
                          <span className="font-semibold">Duration:</span>{" "}
                          {s.duration}
                        </span>
                        <span>
                          <span className="font-semibold">Reward:</span>{" "}
                          {s.reward}
                        </span>
                      </div>
                      {showStartButton && (
                        <Button
                          variant="outlinedDark"
                          className={clsx(
                            "h-8 bg-darkBlueNormal border-0 px-6 text-lightBlue w-full sm:w-auto",
                            startButtonDisabled && "cursor-not-allowed"
                          )}
                          size="sm"
                          disabled={startButtonDisabled}
                          onClick={() => handleStepAction(idx, true)}
                        >
                          <span className="text-body-medium">Start</span>
                        </Button>
                      )}
                      {isCompleted && idx !== activeStep && (
                        <Button
                          variant="outlinedDark"
                          className={clsx(
                            "h-8 text-body-medium bg-darkBlueNormal px-6 text-lightBlue w-full sm:w-auto"
                          )}
                          size="sm"
                          onClick={() => handleStepAction(idx, false)}
                        >
                          <span className="text-body-medium">View</span>
                        </Button>
                      )}
                    </div>
                  </div>
                  {/* Only show details if this is the active step */}
                  {isActive && (
                    <div className="mt-4 w-full px-2 ">
                      {s.addQuestionnaire && s.questions.length > 0 && (
                        <div className="mb-1">
                          {s.questions.map((q: any, qidx: number) => (
                            <div
                              key={qidx}
                              className="flex flex-col w-full pl-4 max-md:pl-0 sm:flex-row items-center sm:items-between sm:justify-between gap-2 mt-1"
                            >
                              <span
                                className={clsx(
                                  "break-words text-body-medium text-paragraphContent",
                                  isActive ? "max-w-[300px]" : "max-w-[200px]"
                                )}
                              >
                                <span className="font-semibold">Question:</span>{" "}
                                {q.text}
                              </span>
                              {q.type === "Valence Scale" && (
                                <span className="flex gap-4 mt-2 sm:mt-0">
                                  {VALENCE_ICONS.map((icon, i) => (
                                    <button
                                      key={i}
                                      type="button"
                                      className={clsx(
                                        "px-2 py-1 rounded border focus:outline-none",
                                        q.response === i
                                          ? "bg-[#47AEA999] border-[#47AEA9]"
                                          : "bg-white border-gray-300",
                                        isActive
                                          ? "cursor-pointer"
                                          : "cursor-default opacity-60"
                                      )}
                                      disabled={!isActive}
                                      onClick={() => handleAnswerSelect(idx, qidx, i)}
                                    >
                                      {icon}
                                    </button>
                                  ))}
                                </span>
                              )}
                              {q.type === "True/False" && (
                                <span className="flex gap-1 mt-2 sm:mt-0">
                                  {TRUE_FALSE_ICONS.map((icon, i) => (
                                    <button
                                      key={i}
                                      type="button"
                                      className={clsx(
                                        "px-2 py-1 rounded border focus:outline-none",
                                        q.response === i
                                          ? "bg-[#47AEA999] border-[#47AEA9]"
                                          : "bg-white border-gray-300",
                                        isActive
                                          ? "cursor-pointer"
                                          : "cursor-default opacity-60"
                                      )}
                                      disabled={!isActive}
                                      onClick={() => handleAnswerSelect(idx, qidx, i)}
                                    >
                                      {icon}
                                    </button>
                                  ))}
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </Card>
              );
            })}
          </div>
          <div className="flex justify-between gap-2 mt-6 w-full">
            <Button
              variant="netural"
              className="h-8 max-md:flex-1 text-lightBlue w-full sm:w-auto"
              onClick={onBack}
            >
              <span className="text-body-medium">Edit Challenge</span>
            </Button>
            <Button
              variant="netural"
              className="h-8 max-md:flex-1 text-body-medium text-lightBlue w-full sm:w-auto"
              onClick={onSave}
            >
              <span className="text-body-medium">Save & Publish</span>
            </Button>
          </div>
        </div>
        {/* Info Card */}
        <div className="w-full md:w-[350px] text-paragraphContent mt-8 md:mt-0">
          <Card className="p-4">
            <div className="flex flex-col">
              <div className="font-bold text-headline-small mb-4">
                {name || "Challenge Name"}
              </div>
              <div className="flex gap-2 items-center mb-2 w-full">
                <span className="text-body-small">Areas of Life:</span>
                <div className="flex">
                  {areas.map((a: string) => (
                    <span key={a}>
                      <AreaIcon area={a} />
                    </span>
                  ))}
                </div>
              </div>
              {renderMedia()}
              <div className="flex justify-between w-full text-body-small mb-1">
                <span>
                  <span className="font-bold">Steps:</span> {steps.length}
                </span>
                <span>
                  <span className="font-bold">Duration:</span>{" "}
                  {steps.reduce(
                    (acc: number, s: any) => acc + parseInt(s.duration),
                    0
                  ) || 0}{" "}
                  Mins
                </span>
                <span>
                  <span className="font-bold">Reward:</span>{" "}
                  {steps.reduce(
                    (acc: number, s: any) => acc + parseInt(s.reward),
                    0
                  ) || 0}{" "}
                  Points
                </span>
              </div>
              {/* Progress bar and lock/check icon */}
              <div className="flex items-center w-full mt-2 gap-1">
                <div className="w-6 h-6 flex-shrink-0 rounded-full flex bg-landingBackground justify-center items-center">
                  {completedSteps === totalSteps ? (
                    <PassedIcon />
                  ) : (
                    <LockIcon />
                  )}
                </div>
                <div className="flex-1 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-[5px] bg-primary rounded-full transition-all"
                    style={{ width: `${progressPercent}%` }}
                  />
                </div>
                <span className="ml-2 text-sm text-gray-700 font-semibold">
                  {completedSteps}/{totalSteps}
                </span>
              </div>
              {/* About the Challenge */}
              <div className="w-full mt-3">
                <div className="font-bold mb-1 text-body-medium">
                  About the Challenge:
                </div>
                <div className="mb-2 text-body-small text-justify">
                  {description || "About the Challenge..."}
                </div>
                <div className="font-bold text-body-medium mb-1 mt-2">
                  About the Instructor:
                </div>
                <div className="flex justify-between items-center py-2 gap-2 mb-1">
                  <div className="flex items-center gap-2">
                    <UserAvatar width={24} height={24} />
                    <span className="font-semibold text-body-medium">
                      {instructor || "Court Potter"}
                    </span>
                  </div>
                  <span className="text-body-small ml-2">Courses: 7</span>
                </div>
                <div className="text-body-small text-justify">
                  Vestibulum augue proin enim cursus montes, ante. Ultrices
                  posuere mattis elit egestas aliquam pellentesque nisl id quis.
                  Turpis volutpat tincidunt volutpat lectus quam facilisi nibh
                  augue egestas.
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
