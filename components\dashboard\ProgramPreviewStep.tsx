"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { Clock, Trophy, Users, Lock, Unlock } from "lucide-react";
import { AreaIcon } from "@/components/dashboard/AreaIcon";

interface Challenge {
  id: string;
  title: string;
  steps: number;
  duration: number;
  reward: number;
  selected: boolean;
}

interface ProgramPreviewStepProps {
  name: string;
  description: string;
  instructor: string;
  areas: string[];
  media: string;
  challenges: Challenge[];
  lockOrder: boolean;
  onBack: () => void;
  onSave: () => void;
}

export default function ProgramPreviewStep({
  name,
  description,
  instructor,
  areas,
  media,
  challenges,
  lockOrder,
  onBack,
  onSave,
}: ProgramPreviewStepProps) {
  const selectedChallenges = challenges.filter(c => c.selected);
  const totalSteps = selectedChallenges.reduce((sum, c) => sum + c.steps, 0);
  const totalReward = selectedChallenges.reduce((sum, c) => sum + c.reward, 0);
  const estimatedDuration = selectedChallenges.length > 0 ? Math.max(...selectedChallenges.map(c => c.duration)) : 0;

  return (
    <div className="flex justify-center w-full">
      <div className="px-2 py-4 sm:p-6 lg:p-8 w-full max-w-full sm:max-w-[700px] mt-4 sm:mt-8">
        <div className="text-body-lg font-bold mb-4 sm:mb-6">
          Preview Program:
        </div>

        {/* Program Card Preview - Fully Responsive */}
        <Card className="p-4 sm:p-6 mb-4 sm:mb-6">
          <div className="flex flex-col lg:flex-row gap-4 sm:gap-6">
            {/* Program Image */}
            <div className="flex-shrink-0">
              {media ? (
                <Image
                  src={media}
                  alt={name}
                  width={200}
                  height={150}
                  className="w-full lg:w-48 h-36 object-cover rounded-lg"
                />
              ) : (
                <div className="w-full lg:w-48 h-36 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 text-sm">No image</span>
                </div>
              )}
            </div>

            {/* Program Details */}
            <div className="flex-1">
              <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">{name || "Untitled Program"}</h2>
              <p className="text-gray-600 mb-3 sm:mb-4 line-clamp-3 text-sm sm:text-base">{description || "No description provided"}</p>

              <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-600 mb-3 sm:mb-4">
                <div className="flex items-center gap-1">
                  <Users className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>By {instructor || "Unknown"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{estimatedDuration} days</span>
                </div>
                <div className="flex items-center gap-1">
                  <Trophy className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{totalReward} pts</span>
                </div>
              </div>

              {/* Areas */}
              {areas.length > 0 && (
                <div className="mb-3 sm:mb-4">
                  <div className="text-xs sm:text-sm font-medium text-gray-700 mb-2">Areas of Life:</div>
                  <div className="flex flex-wrap gap-1 sm:gap-2">
                    {areas.map((area, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1 text-xs">
                        <AreaIcon area={area} />
                        <span>{area}</span>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Program Stats */}
              <div className="grid grid-cols-3 gap-2 sm:gap-4 text-center">
                <div>
                  <div className="text-base sm:text-lg font-bold text-gray-900">{selectedChallenges.length}</div>
                  <div className="text-xs text-gray-600">Challenges</div>
                </div>
                <div>
                  <div className="text-base sm:text-lg font-bold text-gray-900">{totalSteps}</div>
                  <div className="text-xs text-gray-600">Total Steps</div>
                </div>
                <div>
                  <div className="text-base sm:text-lg font-bold text-gray-900">{totalReward}</div>
                  <div className="text-xs text-gray-600">Total Reward</div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Challenge Order Settings - Fully Responsive */}
        <Card className="p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="flex items-center gap-2 mb-2">
            {lockOrder ? (
              <Lock className="w-3 h-3 sm:w-4 sm:h-4 text-orange-500" />
            ) : (
              <Unlock className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
            )}
            <span className="font-medium text-sm sm:text-base">
              {lockOrder ? "Locked Order" : "Flexible Order"}
            </span>
          </div>
          <p className="text-xs sm:text-sm text-gray-600">
            {lockOrder
              ? "Participants must complete challenges in the specified order"
              : "Participants can complete challenges in any order they prefer"
            }
          </p>
        </Card>

        {/* Challenges List - Fully Responsive */}
        <Card className="p-3 sm:p-4 mb-4 sm:mb-6">
          <h3 className="font-medium text-gray-900 mb-3 sm:mb-4 text-sm sm:text-base">
            Program Challenges ({selectedChallenges.length})
          </h3>

          {selectedChallenges.length === 0 ? (
            <div className="text-center py-6 sm:py-8 text-gray-500 text-sm sm:text-base">
              No challenges selected
            </div>
          ) : (
            <div className="space-y-2 sm:space-y-3">
              {selectedChallenges.map((challenge, index) => (
                <div
                  key={challenge.id}
                  className="flex flex-col sm:flex-row sm:items-center justify-between p-2 sm:p-3 bg-gray-50 rounded-lg gap-2 sm:gap-3"
                >
                  <div className="flex items-center gap-2 sm:gap-3 flex-1">
                    <div className="w-5 h-5 sm:w-6 sm:h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0">
                      {index + 1}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="font-medium text-gray-900 text-sm sm:text-base truncate">{challenge.title}</div>
                      <div className="text-xs text-gray-600">
                        {challenge.steps} steps • {challenge.duration} days • {challenge.reward} pts
                      </div>
                    </div>
                  </div>
                  {lockOrder && index > 0 && (
                    <Lock className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400 flex-shrink-0" />
                  )}
                </div>
              ))}
            </div>
          )}
        </Card>

        {/* Program Summary - Fully Responsive */}
        <Card className="p-3 sm:p-4 mb-4 sm:mb-6 bg-blue-50 border-blue-200">
          <h3 className="font-medium text-blue-900 mb-3 text-sm sm:text-base">Program Summary</h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-4 text-xs sm:text-sm">
            <div>
              <div className="text-blue-700 font-medium">Challenges</div>
              <div className="text-blue-900">{selectedChallenges.length}</div>
            </div>
            <div>
              <div className="text-blue-700 font-medium">Total Steps</div>
              <div className="text-blue-900">{totalSteps}</div>
            </div>
            <div>
              <div className="text-blue-700 font-medium">Duration</div>
              <div className="text-blue-900">{estimatedDuration} days</div>
            </div>
            <div>
              <div className="text-blue-700 font-medium">Total Reward</div>
              <div className="text-blue-900">{totalReward} pts</div>
            </div>
          </div>
        </Card>

        {/* Navigation Buttons - Fully Responsive */}
        <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-0">
          <Button
            type="button"
            variant="outlined"
            className="h-8 sm:h-10 rounded-lg text-sm sm:text-base px-4 sm:px-6"
            onClick={onBack}
          >
            Back
          </Button>
          <Button
            type="button"
            variant="netural"
            className="h-8 sm:h-10 rounded-lg text-sm sm:text-base px-4 sm:px-6"
            onClick={onSave}
          >
            Save Program
          </Button>
        </div>
      </div>
    </div>
  );
}
