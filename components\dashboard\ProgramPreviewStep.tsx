"use client"

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, LightRed<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, BlueCheck, DarkBlueCheck, Yellow<PERSON>he<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>C<PERSON><PERSON> } from '@/utils/icons';
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";

export default function ProgramPreviewStep({ setStep }: { setStep: (n: number) => void }) {
  const router = useRouter();
  // Sample data
  const program = {
    title: "Sarah's Program",
    image: "https://images.pexels.com/photos/1051838/pexels-photo-1051838.jpeg",
    challengesCount: 3,
    duration: 90,
    reward: 900,
  };

  const challenges = [
    {
      id: "1",
      title: "Just in Case",
      steps: 30,
      duration: 30,
      reward: 300,
      progress: 0,
    },
  ];

  return (
    <div className="px-2 sm:px-4 md:px-8 pb-8 pt-2 text-left mx-auto w-full max-w-full sm:max-w-[530px] mt-5">
      {/* Heading */}
      <div className="text-lg sm:text-xl font-bold text-gray-900 mb-4">Preview Program:</div>
      <hr className="mb-4 border-gray-300" />

      {/* Preview Card */}
      <div className="bg-mediumGray rounded-xl p-2 max-w-[380px] w-full mx-auto">
        {/* Program Card */}
        <div className="bg-white border border-[#b7c6d6] rounded-lg p-3 sm:p-4 mb-2">
          <div className="font-bold text-lg sm:text-xl text-gray-900 mb-1">{program.title}</div>
          <div className="flex items-center pb-2">
            <div className="text-paragraphContent text-body-medium">Areas of Life:</div>
            <div className="flex -space-x-1 ml-3 items-center">
              <div className='border border-border rounded-full p-1 bg-landingBackground'><GreenCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><RedCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><LightRedCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><BrownCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkBlueCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><YellowCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><PurpleCheck /></div>
              <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkGreenCheck /></div>
            </div>
          </div>
          <img
            src={program.image}
            alt="Program"
            className="w-full h-32 sm:h-40 object-cover rounded mb-2"
          />
          <div className="flex w-full justify-between text-xs">
            <div>
              <span className='font-bold'>Challenges:</span> {program.challengesCount}
            </div>
            <div>
              <span className='font-bold'>Duration:</span> {program.duration} Days
            </div>
            <div>
              <span className='font-bold'>Reward:</span> {program.reward} Points
            </div>
          </div>

        </div>

        {/* Challenges List */}
        {challenges.map((challenge, idx) => (
          <div
            key={challenge.id}
            className="bg-white border border-[#b7c6d6] rounded-lg p-3 sm:p-4 flex flex-col"
          >
            <div className="flex items-center gap-2 mb-2">
              <span className="font-bold text-base sm:text-lg text-gray-900">
                {challenge.title}</span>
            </div>
            <div className="flex items-center pb-2">
              <div className="text-paragraphContent text-body-medium">Areas of Life:</div>
              <div className="flex -space-x-1 ml-3 items-center">
                <div className='border border-border rounded-full p-1 bg-landingBackground'><GreenCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><RedCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><LightRedCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BrownCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><BlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkBlueCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><YellowCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><PurpleCheck /></div>
                <div className='border border-border rounded-full p-1 bg-landingBackground'><DarkGreenCheck /></div>
              </div>
            </div>
            <div className="flex w-full justify-between text-xs">
              <div>
                <span className='font-bold'>Steps:</span> {challenge.steps}
              </div>
              <div>
                <span className='font-bold'>Duration:</span> {challenge.duration} Days
              </div>
              <div>
                <span className='font-bold'>Reward:</span> {challenge.reward} Points
              </div>
            </div>
            {/* Progress and Start Button */}
            <div className="flex items-center gap-2 mt-3">
              <span className="text-gray-500">
                {/* Lock icon */}
                <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                  <rect x="4" y="12" width="16" height="8" rx="2" fill="#e5e7eb" stroke="#6b7a8f" strokeWidth="1.5" />
                  <path d="M8 12V9a4 4 0 1 1 8 0v3" stroke="#6b7a8f" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
              </span>
              <span className="text-xs sm:text-sm text-gray-700 font-semibold">{challenge.progress ?? 0}/{challenge.steps}</span>
              <Button
                type="submit"
                variant="netural"
                className="ml-auto h-8 rounded-lg"
              >Start</Button>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Buttons */}
      <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-0 mt-8">
        <Button
          type="submit"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={() => setStep(2)}
        >
          Back
        </Button>
        <Button
          type="submit"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={() => { router.push("/dashboard/programs"); }}
        >
          Save
        </Button>
      </div>
    </div>
  );
}
