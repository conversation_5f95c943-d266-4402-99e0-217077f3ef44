"use client"

import { ProgramDetail } from '@/components/programs/ProgramDetail'
import { useParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { getProgramById, getProgramChallenges } from '@/lib/programs_mockup'

export default function ProgramDetailPage() {
  const params = useParams()
  const [program, setProgram] = useState<any>(null)
  const [challenges, setChallenges] = useState<any[]>([])

  useEffect(() => {
    if (params.id) {
      const programId = parseInt(params.id as string)
      const foundProgram = getProgramById(programId)
      if (foundProgram) {
        setProgram(foundProgram)
        // Get the challenges associated with this program
        const programChallenges = getProgramChallenges(programId)
        setChallenges(programChallenges)
      }
    }
  }, [params.id])

  if (!program) {
    return <div className="p-8 text-center">Loading program details...</div>
  }

  return (
    <ProgramDetail
      id={program.id}
      title={program.title}
      image={program.image}
      challenges={challenges} // Pass the actual challenge objects
      description={program.description}
      instructor={program.instructor}
      totalDuration={program.totalDuration}
      totalReward={program.totalReward}
    />
  )
}
