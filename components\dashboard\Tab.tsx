"use client"

interface TabProps {
  label: string
  active: boolean
  onClick: () => void
  count?: number
}

export function Tab({ label, active, onClick, count }: TabProps) {
  return (
    <button
      onClick={onClick}
      className={`
        flex-1 sm:flex-none
        px-2 sm:px-3 lg:px-4
        py-2 sm:py-2.5 lg:py-3
        text-xs sm:text-sm lg:text-base
        font-medium
        whitespace-nowrap
        transition-all duration-200
        text-left
        max-md:text-center
        min-w-0
        relative
        ${active
          ? "text-darkerBlue border-b-2 border-darkerBlue bg-blue-50/30"
          : "text-darkBlueNormal hover:text-darkerBlue hover:bg-gray-50"
        }
      `}
    >
      <span className="truncate block">{label}</span>
      {count && (
        <span className={`
          ml-1 sm:ml-1.5 lg:ml-2
          ${active ? "bg-[#3ABAB4] text-white" : "bg-gray-400 text-white"}
          text-xs
          px-1 sm:px-1.5 lg:px-2
          py-0.5 sm:py-1
          rounded-full
          inline-block
          min-w-[16px] sm:min-w-[20px]
          text-center
        `}>
          {count > 99 ? "99+" : count}
        </span>
      )}
    </button>
  )
}
