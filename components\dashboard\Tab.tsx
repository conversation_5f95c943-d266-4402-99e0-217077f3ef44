"use client"

interface TabProps {
  label: string
  active: boolean
  onClick: () => void
  count?: number
}

export function Tab({ label, active, onClick, count }: TabProps) {
  return (
    <button
      onClick={onClick}
      className={`flex sm:flex-none px-3 py-1 md:px-4 md:py-3 text-sm md:text-base font-medium whitespace-nowrap transition-all duration-200 text-left ${
        active
          ? "text-darkerBlue border-b-2 border-darkerBlue"
          : "text-darkBlueNormal hover:text-darkerBlue"
      }`}
    >
      <span className="truncate">{label}</span>
      {count && (
        <span className={`ml-1 md:ml-2 ${active ? "bg-[#3ABAB4] text-white" : "bg-gray-400 text-white"} text-xs px-1.5 md:px-2 py-1 rounded-full`}>
          {count > 99 ? "99+" : count}
        </span>
      )}
    </button>
  )
}
