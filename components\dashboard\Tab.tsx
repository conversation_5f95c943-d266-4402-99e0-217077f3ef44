"use client"

interface TabProps {
  label: string
  active: boolean
  onClick: () => void
  count?: number
}

export function Tab({ label, active, onClick, count }: TabProps) {
  return (
    <button
      onClick={onClick}
      className={`px-2 py-1 md:px-4 md:py-3 text-xs md:text-sm font-medium whitespace-nowrap ${
        active
          ? "text-darkerBlue border-b-2 border-darkerBlue"
          : "text-paragraphContent hover:text-gray-700"
      }`}
    >
      {label}
      {count && (
        <span className={`ml-1 md:ml-2 ${active ? "bg-[#3ABAB4] text-white" : "bg-gray-500 text-white"} text-xs px-1.5 md:px-2 py-1 rounded-full`}>
          {count > 99 ? "99+" : count}
        </span>
      )}
    </button>
  )
}
