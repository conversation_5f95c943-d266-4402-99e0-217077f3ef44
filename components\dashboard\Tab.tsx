"use client"

interface TabProps {
  label: string
  active: boolean
  onClick: () => void
  count?: number
}

export function Tab({ label, active, onClick, count }: TabProps) {
  return (
    <button
      onClick={onClick}
      className={`flex-1 sm:flex-none px-3 py-2 md:px-4 md:py-3 text-sm md:text-base font-medium whitespace-nowrap transition-colors duration-200 ${
        active
          ? "text-darkerBlue border-b-2 border-darkerBlue bg-blue-50 sm:bg-transparent"
          : "text-paragraphContent hover:text-gray-700 hover:bg-gray-50"
      }`}
    >
      <span className="truncate">{label}</span>
      {count && (
        <span className={`ml-1 md:ml-2 ${active ? "bg-[#3ABAB4] text-white" : "bg-gray-500 text-white"} text-xs px-1.5 md:px-2 py-1 rounded-full`}>
          {count > 99 ? "99+" : count}
        </span>
      )}
    </button>
  )
}
