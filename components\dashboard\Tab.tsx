"use client"

interface TabProps {
  label: string
  active: boolean
  onClick: () => void
  count?: number
}

export function Tab({ label, active, onClick, count }: TabProps) {
  return (
    <button
      onClick={onClick}
      className={`flex-1 sm:flex-none px-3 py-2 md:px-4 md:py-3 text-sm md:text-base font-semibold whitespace-nowrap transition-all duration-200 ${
        active
          ? "text-darkerBlue border-b-3 border-darkerBlue bg-darkerBlue sm:bg-transparent shadow-sm"
          : "text-gray-500 hover:text-paragraphContent hover:bg-gray-50 border-b-3 border-transparent"
      }`}
    >
      <span className="truncate">{label}</span>
      {count && (
        <span className={`ml-1 md:ml-2 ${active ? "bg-[#3ABAB4] text-white" : "bg-gray-400 text-white"} text-xs px-1.5 md:px-2 py-1 rounded-full`}>
          {count > 99 ? "99+" : count}
        </span>
      )}
    </button>
  )
}
