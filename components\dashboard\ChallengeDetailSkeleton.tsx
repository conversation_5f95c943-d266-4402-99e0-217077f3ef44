import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

export function ChallengeDetailSkeleton() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Steps List Skeleton */}
      <div className="lg:col-span-2">
        <div className="flex flex-col gap-2">
          {Array.from({ length: 11 }).map((_, idx) => (
            <div key={idx} className="flex flex-col sm:flex-row sm:items-center justify-between bg-white rounded border border-gray-200 p-2 pl-0 text-sm font-medium">
              <div className="flex items-center gap-2">
                <Skeleton width={24} height={20} />
                <Skeleton width={180} height={20} />
              </div>
              <div className="flex flex-wrap gap-2 items-center">
                <Skeleton width={70} height={16} />
                <Skeleton width={90} height={16} />
                <Skeleton width={80} height={16} />
                <Skeleton width={60} height={32} borderRadius={8} />
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* Challenge Card Skeleton */}
      <div className="bg-white rounded border border-gray-200 shadow p-4 flex flex-col w-[370px]">
        <div className="flex items-center justify-between mb-2">
          <Skeleton width={200} height={28} />
          <Skeleton width={24} height={24} circle />
        </div>
        <div className="text-xs text-gray-600 mb-1 flex items-center gap-1">
          <span className="font-bold">Areas of Life:</span>
          <div className="flex items-center gap-1">
            {Array.from({ length: 9 }).map((_, i) => (
              <Skeleton key={i} width={24} height={24} circle />
            ))}
          </div>
        </div>
        <div className="relative w-full h-48 overflow-hidden mb-3">
          <Skeleton height={192} />
        </div>
        <div className="flex flex-wrap items-center text-xs text-gray-700 mb-2 gap-2">
          <Skeleton width={60} height={16} />
          <Skeleton width={80} height={16} />
          <Skeleton width={90} height={16} />
        </div>
        <div className="flex items-center gap-1 mb-3">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} width={24} height={24} circle />
          ))}
          <Skeleton width={24} height={24} circle />
        </div>
        <div className="mb-3">
          <Skeleton width={120} height={18} />
          <Skeleton count={2} height={14} className="mt-1" />
        </div>
        <div>
          <Skeleton width={140} height={18} />
          <div className="flex items-center gap-2 mb-1 mt-1">
            <Skeleton width={80} height={14} />
            <Skeleton width={60} height={14} />
          </div>
          <Skeleton count={2} height={14} />
        </div>
      </div>
    </div>
  );
} 