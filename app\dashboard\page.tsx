'use client';

import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { PatientDashboard } from '@/components/dashboard/PatientDashboard';
import { ProviderDashboard } from '@/components/dashboard/ProviderDashboard';

export default function Dashboard() {
  const userRole = useSelector((state: RootState) => state.user.user?.role);

  if (userRole === 'patient') {
    return <PatientDashboard />;
  }

  if (userRole === 'provider') {
    return <ProviderDashboard />;
  }
}
