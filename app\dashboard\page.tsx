"use client";

import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { PatientDashboard } from "@/components/dashboard/patient/page";
import { ProviderDashboard } from "@/components/dashboard/provider/page";
import { useSearchParams } from "next/navigation";

export default function Dashboard() {
  const userRole = useSelector((state: RootState) => state.user.user?.role);
  const searchParams = useSearchParams();
  const patientId = searchParams.get("patientId");

  if (userRole === "patient") {
    return <PatientDashboard patientId={patientId ? parseInt(patientId) : 0} />;
  }

  if (userRole === "provider") {
    return (
      patientId ? (
        <PatientDashboard patientId={patientId ? parseInt(patientId) : 0} />
      ) : (
        <ProviderDashboard />
      )
    );
  }
}
