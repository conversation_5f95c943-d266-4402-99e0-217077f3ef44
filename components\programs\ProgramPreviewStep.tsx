"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { AreaIcon } from "@/components/dashboard/AreaIcon";

interface Challenge {
  id: string;
  title: string;
  steps: number;
  duration: number;
  reward: number;
  selected: boolean;
}

interface ProgramPreviewStepProps {
  name: string;
  description: string;
  instructor: string;
  areas: string[];
  media: string;
  challenges: Challenge[];
  lockOrder: boolean;
  onBack: () => void;
  onSave: () => void;
}

export default function ProgramPreviewStep({
  name,
  areas,
  media,
  challenges,
  onBack,
  onSave,
}: ProgramPreviewStepProps) {
  const selectedChallenges = challenges.filter(c => c.selected);
  const totalReward = selectedChallenges.reduce((sum, c) => sum + c.reward, 0);
  const estimatedDuration = selectedChallenges.length > 0 ? Math.max(...selectedChallenges.map(c => c.duration)) : 0;

  return (
    <div className="px-2 sm:px-4 md:px-8 pb-8 pt-2 text-left mx-auto w-full max-w-full sm:max-w-[650px] mt-5">
      {/* Heading */}
      <div className="text-lg sm:text-xl font-bold text-gray-900 mb-4">Preview Program:</div>
      <hr className="mb-4 border-gray-300" />

      {/* Scrollable Preview Container */}
      <div className="bg-mediumGray rounded-xl p-2 max-w-[450px] w-full mx-auto max-h-[500px] overflow-y-auto">
        {/* Program Card */}
        <div className="bg-white border border-[#b7c6d6] rounded-lg p-3 sm:p-4 mb-2">
          <div className="font-bold text-lg sm:text-xl text-gray-900 mb-1">{name || "Sarah's Program"}</div>
          <div className="flex items-center pb-2">
            <div className="text-paragraphContent text-body-medium">Areas of Life:</div>
            <div className="flex -space-x-1 ml-3 items-center">
              {areas.map((area, index) => (
                <AreaIcon key={index} area={area} />
              ))}
            </div>
          </div>
          {media ? (
            <img
              src={media}
              alt="Program"
              className="w-full h-32 sm:h-40 object-cover rounded mb-2"
            />
          ) : (
            <div className="w-full h-32 sm:h-40 bg-gray-200 rounded mb-2 flex items-center justify-center">
              <span className="text-gray-500 text-sm">No image</span>
            </div>
          )}
          <div className="flex w-full justify-between text-xs">
            <div>
              <span className='font-bold'>Challenges:</span> {selectedChallenges.length}
            </div>
            <div>
              <span className='font-bold'>Duration:</span> {estimatedDuration} Days
            </div>
            <div>
              <span className='font-bold'>Reward:</span> {totalReward} Points
            </div>
          </div>
        </div>

        {/* Individual Challenge Cards */}
        {selectedChallenges.map((challenge, index) => (
          <div
            key={challenge.id}
            className="bg-white border border-[#b7c6d6] rounded-lg p-3 sm:p-4 mb-2"
          >
            <div className="flex items-center gap-2 mb-2">
              <span className="font-bold text-base sm:text-lg text-gray-900">
                {index + 1}. {challenge.title}
              </span>
            </div>
            <div className="flex items-center pb-2">
              <div className="text-paragraphContent text-body-medium">Areas of Life:</div>
              <div className="flex -space-x-1 ml-3 items-center">
                {areas.map((area, areaIndex) => (
                  <AreaIcon key={areaIndex} area={area} />
                ))}
              </div>
            </div>
            <div className="flex w-full justify-between text-xs mb-3">
              <div>
                <span className='font-bold'>Steps:</span> {challenge.steps}
              </div>
              <div>
                <span className='font-bold'>Duration:</span> {challenge.duration} Days
              </div>
              <div>
                <span className='font-bold'>Reward:</span> {challenge.reward} Points
              </div>
            </div>
            {/* Progress and Start Button */}
            <div className="flex items-center gap-2">
              <span className="text-gray-500">
                {/* Lock icon */}
                <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                  <rect x="4" y="12" width="16" height="8" rx="2" fill="#e5e7eb" stroke="#6b7a8f" strokeWidth="1.5" />
                  <path d="M8 12V9a4 4 0 1 1 8 0v3" stroke="#6b7a8f" strokeWidth="1.5" strokeLinecap="round" />
                </svg>
              </span>
              <span className="text-xs sm:text-sm text-gray-700 font-semibold">0/{challenge.steps}</span>
              <Button
                type="button"
                variant="netural"
                className="ml-auto h-8 rounded-lg"
              >
                Start
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Buttons - Outside scrollable area */}
      <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-0 mt-8">
        <Button
          type="button"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={onBack}
        >
          Back
        </Button>
        <Button
          type="button"
          variant="netural"
          className="h-8 rounded-lg"
          onClick={onSave}
        >
          Save
        </Button>
      </div>
    </div>
  );
}
