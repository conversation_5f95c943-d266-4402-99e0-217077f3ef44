"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { LockOpen } from "@/utils/icons";

interface Challenge {
  id: string;
  title: string;
  steps: number;
  duration: number;
  reward: number;
  selected: boolean;
}

interface ProgramPreviewStepProps {
  name: string;
  description: string;
  media: string;
  challenges: Challenge[];
  areasOfLifeIcons: React.ReactElement[];
  onBack: () => void;
  onSave: () => void;
}

export default function ProgramPreviewStep({
  name,
  media,
  challenges,
  areasOfLifeIcons,
  onBack,
  onSave,
}: ProgramPreviewStepProps) {
  const selectedChallenges = challenges.filter(c => c.selected);
  const totalReward = selectedChallenges.reduce((sum, c) => sum + c.reward, 0);
  const totalDuration = selectedChallenges.reduce((sum, c) => sum + c.duration, 0);

  return (
    <div className="px-2 sm:px-4 md:px-8 pb-8 p-2 text-left mx-auto w-full max-w-full sm:max-w-[650px] mt-5">
      {/* Heading */}
      <div className="text-lg sm:text-xl font-bold text-gray-900 mb-4">Preview Program:</div>
      <hr className="mb-4 border-gray-300" />

      {/* Scrollable Preview Container */}
      <div className="bg-mediumGray rounded-xl p-2 max-w-[450px] w-full mx-auto max-h-[500px] overflow-y-auto">
        {/* Program Card */}
        <div className="bg-white border border-[#b7c6d6] rounded-lg p-3 sm:p-4">
          <div className="font-bold text-lg sm:text-xl text-gray-900 mb-1">{name || "Sarah's Program"}</div>
          <div className="flex items-center pb-2">
            <div className="text-paragraphContent text-body-medium">Areas of Life:</div>
            <div className="flex -space-x-1 ml-3 items-center">
              {areasOfLifeIcons.map((icon, index) => (
                <div key={index} className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'>
                  {icon}
                </div>
              ))}
            </div>
          </div>
          {media ? (
            <img
              src={media}
              alt="Program"
              className="w-full sm:h-56 mb-2"
            />
          ) : (
            <div className="w-full bg-gray-200 rounded mb-2 flex items-center justify-center">
              <span className="text-gray-500 text-sm">No image</span>
            </div>
          )}
          <div className="flex w-full justify-between text-body-medium">
            <div>
              <span className='font-bold'>Challenges:</span> {selectedChallenges.length}
            </div>
            <div>
              <span className='font-bold'>Duration:</span> {totalDuration} Days
            </div>
            <div>
              <span className='font-bold'>Reward:</span> {totalReward} Points
            </div>
          </div>
        </div>

        {/* Individual Challenge Cards */}
        <div className="space-y-2 mt-2">
          {selectedChallenges.map((challenge, index) => (
            <div
              key={challenge.id}
              className="bg-white border border-[#b7c6d6] rounded-lg p-3 sm:p-4"
            >
              <div className="flex items-center gap-2 mb-2">
                <span className="font-bold text-base sm:text-lg text-gray-900">
                  {index + 1}. {challenge.title}
                </span>
              </div>
              <div className="flex items-center pb-2">
                <div className="text-paragraphContent text-body-medium">Areas of Life:</div>
                <div className="flex -space-x-1 ml-3 items-center">
                  {areasOfLifeIcons.map((icon, areaIndex) => (
                    <div key={areaIndex} className='border border-gray-300 rounded-full p-1 bg-[#edf1f5]'>
                      {icon}
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex w-full justify-between text-body-medium mb-3">
                <div>
                  <span className='font-bold'>Steps:</span> {challenge.steps}
                </div>
                <div>
                  <span className='font-bold'>Duration:</span> {challenge.duration} Days
                </div>
                <div>
                  <span className='font-bold'>Reward:</span> {challenge.reward} Points
                </div>
              </div>

              {/* Lock Icon and Start Button */}
              <div className="flex items-center max-md:flex-col gap-2">
                <div className="bg-landingBackground border border-border rounded-full p-1">
                  {/* Lock icon */}
                  <LockOpen />
                </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(0 / challenge.steps) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-md">0/{challenge.steps}</span>
                <Button
                  variant="netural"
                  size="sm"
                  className="ml-auto text-lightBlue h-8 w-full sm:w-auto px-2 md:px-6"
                >
                  <span className="text-body-medium">Start</span>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Buttons - Outside scrollable area */}
      <div className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-0 mt-8">
        <Button
          variant="netural"
          size="sm"
          className="text-lightBlue h-8 w-full sm:w-auto px-6"
          onClick={onBack}
        >
          <span className="text-body-medium">Back</span>
        </Button>
        <Button
          variant="netural"
          size="sm"
          className="text-lightBlue h-8 w-full sm:w-auto px-6"
          onClick={onSave}
        >
          <span className="text-body-medium">Save</span>
        </Button>
      </div>
    </div>
  );
}
