"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { LandingIcon1, LandingIcon2, LandingIcon3, LandingIcon4, LandingIcon5, LandingIcon6, LandingIcon7, <PERSON>ArrowNext, LandingArrowPrev} from "@/utils/icons"

const slides = [
  {
    key: "spotify",
    content: (
      <div className="flex flex-col md:flex-row items-center px-16 max-md:px-0">
        <div className="flex-1 py-16 max-md:py-10">
          <div className="flex items-center gap-2 mb-2">
            <LandingIcon1 />
            <p className="font-title-medium text-neturalDark">FEATURED PLAYLIST</p>
          </div>
          <p className="max-md:text-3xl font-bold text-neturalDark text-display-lg mb-2">Peakality Motivation #1</p>
          <p className="text-lg text-neturalDark mb-2 max-w-[633px]">Immerse yourself in our exclusive Spotify playlist - designed to inspire creativity, boost productivity or simply help you unwind when you need it most - while we build the next chapter of Peakality.</p>
          <div className="flex items-center gap-6 text-neturalDark text-sm mb-2">
            <p className="flex items-center gap-1 text-neturalDark"><LandingIcon2 />124 tracks</p>
            <p className="flex items-center gap-1 text-neturalDark"><LandingIcon3 />6 hours</p>
            <p className="flex items-center gap-1 text-neturalDark"><LandingIcon4 />45.2K likes</p>
          </div>
          <Button variant="primary" className="px-8 mt-8 text-lightBlue max-md:mx-auto">Play on Spotify</Button>
        </div>
        <div className="flex max-w-[500px] max-md:mb-4">
          <Image src="/landing/img1.png" alt="Animal" width={240} height={457} className="object-cover h-[457px]" />
          <Image src="/landing/img2.png" alt="Fighter" width={240} height={457} className="object-cover h-[457px]" />
        </div>
      </div>
    ),
  },
  {
    key: "amazon",
    content: (
      <div className="flex flex-col md:flex-row items-center w-full px-16 max-md:px-0 min-h-[457px] max-md:h-max">
        <div className="flex-1 py-20 max-md:py-10">
          <div className="flex items-center gap-3">
            <LandingIcon5 />
            <p className="text-title-medium text-neturalDark font-bold">AMAZON STORE</p>
          </div>
          <p className="text-neturalDark text-display-lg mb-2 max-md:text-3xl">Stay motivated, achieve more</p>
          <p className="text-lg text-neturalDark mb-2">Explore our handpicked Amazon selection - each product has been selected to keep you motivated and empowered, providing the perfect tools and resources for your journey as we build the future of Peakality.</p>
          <div className="flex items-center gap-6 text-neturalDark font-title-medium">
            <span className="flex items-center gap-1 text-neturalDark"><LandingIcon6 />Secure shopping</span>
            <span className="flex items-center gap-1 text-neturalDark"><LandingIcon3 />24/7 Support</span>
            <span className="flex items-center gap-1 text-neturalDark"><LandingIcon7 />Easy returns</span>
          </div>
          <Button variant="primary" className="px-8 text-lightBlue mt-8">Shop Now</Button>
        </div>
        <div className="flex-1 max-w-[370px] justify-center items-center">
          <Image src="/landing/img3.png" alt="Atomic Habits" width={370} height={340} className="object-cover" />
        </div>
      </div>
    ),
  },
]

export function PromoSlider() {
  const [index, setIndex] = useState(0)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const next = () => setIndex((i) => (i + 1) % slides.length)
  const prev = () => setIndex((i) => (i - 1 + slides.length) % slides.length)

  // Autoplay logic
  useEffect(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current)
    timeoutRef.current = setTimeout(() => {
      setIndex((i) => (i + 1) % slides.length)
    }, 8000)
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
    }
  }, [index])

  return (
    <div className="max-md:px-6 min-h-[457px] relative container">
      <div className="bg-white flex items-center justify-center relative">
        {/* Prev Button */}
        <button
          onClick={() => { prev(); if (timeoutRef.current) clearTimeout(timeoutRef.current); }}
          className="absolute -left-4 max-md:hidden top-1/2 -translate-y-1/2 z-10 p-0 w-12 h-12 "
          aria-label="Previous slide"
        >
          <LandingArrowNext />
        </button>
        {/* Next Button */}
        <button
          onClick={() => { next(); if (timeoutRef.current) clearTimeout(timeoutRef.current); }}
          className="absolute -right-4 max-md:hidden top-1/2 -translate-y-1/2 z-10 p-0 w-12 h-12"
          aria-label="Next slide"
        >
          <LandingArrowPrev />
        </button>
        <AnimatePresence mode="wait">
          <motion.div
            key={slides[index].key}
            initial={{ opacity: 0, x: 0 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 0 }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
            className="w-full"
          >
            {slides[index].content}
          </motion.div>
        </AnimatePresence>
      </div>
      {/* Dots */}
      <div className="absolute bottom-16 left-1/2 -translate-x-1/2 flex justify-center items-center gap-3">
        {slides.map((_, i) => (
          <button
            key={i}
            onClick={() => { setIndex(i); if (timeoutRef.current) clearTimeout(timeoutRef.current); }}
            className={`w-3 h-3 rounded-full transition ${i === index ? "bg-darkBlueNormal" : "bg-darkBlue"}`}
            aria-label={`Go to slide ${i + 1}`}
          />
        ))}
      </div>
    </div>
  )
} 