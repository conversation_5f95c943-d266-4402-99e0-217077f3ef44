"use client"

import { ProgramCard } from './ProgramCard';
import { ChallengeCard } from './ChallengeCard';
import { Programs } from '@/lib/programs_mockup'

export function ProgramsContent() {

  return (
    <div className="px-1 py-2 grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
      {Programs.map((program, index) => (
        <div key={index}>
          <div className='md:max-w-[360px] w-full bg-darkBlueBackground p-2 rounded-lg'>
            <ProgramCard
              id={program.id}
              title={program.title}
              image={program.image}
              challenges={program.challenges.length}
              duration={program.challenges[0].duration}
              reward={program.challenges[0].reward}
            />
            <div className="space-y-2 mt-2">
              {program.challenges.map((challenge, index1) => (
                <ChallengeCard
                  key={index1}
                  id={challenge.id}
                  title={challenge.title}
                  steps={challenge.steps}
                  duration={challenge.duration}
                  reward={challenge.reward}
                />
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
