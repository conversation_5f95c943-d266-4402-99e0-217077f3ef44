"use client"

import { ProgramCard } from './ProgramCard'

export function ProgramsContent() {
  const programs = [
    {
      id: 1,
      title: 'Welcome to Peakality',
      image: '/dashboard/driver.svg',
      challenges: [
        { id: 1, title: 'Wheel of Life Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 2, title: '33 Questions Challenge', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 3, title: 'Decision Destination', steps: 30, duration: 30, reward: 900, progress: 0 },
      ],
      isFirst: true
    },
    {
      id: 2,
      title: '<PERSON>’s Program',
      image: '/dashboard/pray.svg',
      challenges: [
        { id: 1, title: 'Just in Case', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 2, title: 'Pulling it Together', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 3, title: 'The Toolkit', steps: 30, duration: 30, reward: 900, progress: 0 },
      ],
      isFirst: true
    },
    {
      id: 3,
      title: '<PERSON>’s Program',
      image: '/dashboard/climb.svg',
      challenges: [
        { id: 1, title: 'Just in Case', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 2, title: 'Pulling it Together', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 3, title: 'The Toolkit', steps: 30, duration: 30, reward: 900, progress: 0 },
      ],
      isFirst: true
    },
    {
      id: 4,
      title: 'Sarah’s Program',
      image: '/dashboard/sea.svg',
      challenges: [
        { id: 1, title: 'Just in Case', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 2, title: 'Pulling it Together', steps: 30, duration: 30, reward: 900, progress: 0 },
        { id: 3, title: 'The Toolkit', steps: 30, duration: 30, reward: 900, progress: 0 },
      ],
      isFirst: true
    }
  ];

  return (
    <div className="px-1 py-2 grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
      {programs.map((program, index) => (
        <div key={index}>
          <div className='md:max-w-[360px] w-full bg-darkBlueBackground p-2 rounded-lg'>
            <ProgramCard
              id={program.id}
              title={program.title}
              image={program.image}
              challenges={program.challenges.length}
              duration={program.challenges[0].duration}
              reward={program.challenges[0].reward}
              isFirst={program.isFirst}
            />
            <div className="space-y-2 mt-2">
              {program.challenges.map((challenge, index1) => (
                <ProgramCard
                  key={index1}
                  id={challenge.id}
                  title={challenge.title}
                  challenges={challenge.steps}
                  duration={challenge.duration}
                  reward={challenge.reward}
                />
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
