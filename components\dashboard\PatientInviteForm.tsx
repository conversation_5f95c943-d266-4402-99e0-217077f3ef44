"use client"

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useState } from "react";

export default function PatientInviteForm({
  form,
  setForm,
  errors: parentErrors,
}: any) {
  const [errors, setErrors] = useState<any>({});
  const validate = () => {
    const errs: any = {};
    if (!form.email && !form.phone) errs.email = "Email or phone is required";
    if (form.email && !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email))
      errs.email = "Invalid email";
    if (form.phone && !/^\+?\d{7,}$/.test(form.phone))
      errs.phone = "Invalid phone number";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };
  return (
    <form
      onSubmit={e => {
        e.preventDefault();
        if (validate()) {
          console.log("Patient Data:", form);
        }
      }}
      className="max-w-[500px] p-6 mx-auto py-16"
    >
      <p className="text-body-lg font-bold text-netural90 mb-2">
        Your patient plan was successfully purchased!
      </p>
      <div className="text-body-medium text-netural90 mb-2">
        We sent you a confirmation email, and you can now see your new
        patient in{" "}
        <a
          href="/dashboard/patients"
          className="text-blue-700 underline font-medium"
        >
          My Patients
        </a>
        .
      </div>
      <div className="text-body-lg font-bold text-netural90 mb-4">
        Now just invite your patient to their account:
      </div>
      <label className="text-body-medium text-netural90 font-bold">Patient Email</label>
      <Input
        className={`w-full mb-2 p-2 mt-2 sm:p-3 bg-landingBackground ${errors.email ? "border-b-2 border-red-400" : ""}`}
        name="email"
        value={form.email}
        onChange={e => setForm((f: any) => ({ ...f, email: e.target.value }))}
        placeholder=""
        autoComplete="off"
      />
      {errors.email && (
        <div className="text-red-500 text-sm mb-2">{errors.email}</div>
      )}
      <div className="text-center my-4 text-netural90 font-bold">
        OR
      </div>
      <label className="text-body-medium text-netural90 font-bold">Patient Phone</label>
      <Input
        className={`w-full mb-4 p-2 mt-2 sm:p-3 bg-landingBackground ${errors.phone ? "border-b-2 border-red-400" : ""}`}
        name="phone"
        value={form.phone}
        onChange={e => setForm((f: any) => ({ ...f, phone: e.target.value }))}
        placeholder=""
        autoComplete="off"
      />
      {errors.phone && (
        <div className="text-red-500 text-sm mb-2">{errors.phone}</div>
      )}
      <div className="flex justify-end mt-12">
        <Button
          variant="netural"
          size="sm"
          type="submit"
          className="text-label-large h-8"
        >
          <span className="text-lightBlue ">Send Invite</span>
        </Button>
      </div>
    </form>
  );
}
