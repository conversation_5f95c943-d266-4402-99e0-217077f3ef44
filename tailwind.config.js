/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    container: {
      center: true,
      padding: '5em',
      screens: {
        DEFAULT: '1440px'
      }
    },
    extend: {
      fontFamily: {
        sans: ['Roboto', 'sans-serif'], // Sets Roboto as default font
      },
      colors: {
        landingBackground: "var(--landing-background)",
        border: "var(--border)",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        paragraphContent: "var(--paragraph-content)",
        assessmentTitle: "var(--assessment-title)",
        card: "hsl(var(--card))",
        cardForeground: "hsl(var(--card-foreground))",
        primary: "var(--primary)",
        secondary: "var(--secondary)",
        neturalDark: "var(--neutral)",
        neturalLight: "var(--neutral-light)",
        netural20: "var(--netural-20)",
        netural40: "var(--netural-40)",
        netural90: "var(--netural-90)",
        netural100: "var(--netural-100)",
        wheelPhysical: "var(--wheel-physical)",
        wheelLove: "var(--wheel-love)",
        wheelSocial: "var(--wheel-social)",
        wheelEmotional: "var(--wheel-emotional)",
        wheelFamily: "var(--wheel-family)",
        wheelWork: "var(--wheel-work)",
        wheelCreative: "var(--wheel-creative)",
        wheelSpiritual: "var(--wheel-spiritual)",
        wheelFinancial: "var(--wheel-financial)",
        coreGreen: "var(--core-green)",
        coreGreenHover: "var(--core-green-hover)",
        coreGreenActive: "var(--core-green-active)",
        coreGreenDisabled: "var(--core-green-disabled)",
        coreGreenNormal: "var(--core-green-normal)",
        coreGreenNormalHover: "var(--core-green-normal-hover)",
        coreGreenNormalActive: "var(--core-green-normal-active)",
        coreGreenLight: "var(--core-green-light)",
        coreGreenLightHover: "var(--core-green-light-hover)",
        coreGreenLightActive: "var(--core-green-light-active)",
        coreGreenDark: "var(--core-green-dark)",
        coreGreenDarkHover: "var(--core-green-dark-hover)",
        coreGreenDarkActive: "var(--core-green-dark-active)",
        influencerGold: "var(--influencer-gold)",
        influencerGoldHover: "var(--influencer-gold-hover)",
        influencerGoldActive: "var(--influencer-gold-active)",
        influencerGoldNormal: "var(--influencer-gold-normal)",
        influencerGoldNormalHover: "var(--influencer-gold-normal-hover)",
        influencerGoldNormalActive: "var(--influencer-gold-normal-active)",
        influencerGoldLight: "var(--influencer-gold-light)",
        influencerGoldLightHover: "var(--influencer-gold-light-hover)",
        influencerGoldLightActive: "var(--influencer-gold-light-active)",
        influencerGoldDark: "var(--influencer-gold-dark)",
        influencerGoldDarkHover: "var(--influencer-gold-dark-hover)",
        influencerGoldDarkActive: "var(--influencer-gold-dark-active)",
        lightBlue: "var(--light-blue)",
        lightBlueHover: "var(--light-blue-hover)",
        lightBlueActive: "var(--light-blue-active)",
        lightBlueDisabled: "var(--light-blue-disabled)",
        lightBlueNormal: "var(--light-blue-normal)",
        lightBlueNormalHover: "var(--light-blue-normal-hover)",
        lightBlueNormalActive: "var(--light-blue-normal-active)",
        lightBlueLight: "var(--light-blue-light)",
        lightBlueLightHover: "var(--light-blue-light-hover)",
        lightBlueLightActive: "var(--light-blue-light-active)",
        lightBlueDark: "var(--light-blue-dark)",
        lightBlueDarkHover: "var(--light-blue-dark-hover)",
        lightBlueDarkActive: "var(--light-blue-dark-active)",
        darkBlue: "var(--dark-blue)",
        darkBlueHover: "var(--dark-blue-hover)",
        darkBlueActive: "var(--dark-blue-active)",
        darkBlueNormal: "var(--dark-blue-normal)",
        darkBlueNormalHover: "var(--dark-blue-normal-hover)",
        darkBlueNormalActive: "var(--dark-blue-normal-active)",
        darkBlueLight: "var(--dark-blue-light)",
        darkBlueLightHover: "var(--dark-blue-light-hover)",
        darkBlueLightActive: "var(--dark-blue-light-active)",
        darkBlueDark: "var(--dark-blue-dark)",
        darkBlueDarkHover: "var(--dark-blue-dark-hover)",
        darkBlueDarkActive: "var(--dark-blue-dark-active)",
        contentColor: "var(--content-color)",
        pagenationColor: "var(--pagenation-color)",
        pagenationHoverColor: "var(--pagenation-hover-color)",
        coolGrey400: "var(--cool-grey-400)",
        mediumGray: "var(--medium-gray)",
        darkerBlue: "var(--darker-blue)",
        oldBlue: "var(--old-blue)",
        paragraphContent: "var(--paragraph-content)",
        darkBlueBackground: "var(--dark-blue-background)",
        oldN30: "var(--old-n30)",
        coolGray60: "var(--cool-gray-60)",
      },
      borderRadius: {
        lx: "calc(var(--radius) + 16px)",
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      fontFamily: {
        roboto: ['Roboto', 'sans-serif'],
      },
      fontSize: {
        'display-xl': ['64px', { lineHeight: '72px', letterSpacing: '-0.5px', fontWeight: '400' }],
        // Display
        'display-lg': ['54px', { lineHeight: '64px', letterSpacing: '0px', fontWeight: '700' }],
        'display-large': ['57px', { lineHeight: '64px', letterSpacing: '0px', fontWeight: '400' }],
        'display-md': ['42px', { lineHeight: '110%', letterSpacing: '0px', fontWeight: '700' }],
        'display-medium': ['45px', { lineHeight: '52px', letterSpacing: '0px', fontWeight: '400' }],
        'display-small': ['36px', { lineHeight: '44px', letterSpacing: '0px', fontWeight: '400' }],
        // Headline
        'headline-large': ['32px', { lineHeight: '40px', letterSpacing: '0px', fontWeight: '400' }],
        'headline-medium': ['28px', { lineHeight: '36px', letterSpacing: '0px', fontWeight: '400' }],
        'headline-small': ['24px', { lineHeight: '32px', letterSpacing: '0px', fontWeight: '400' }],
        // Title
        'title-large': ['22px', { lineHeight: '28px', letterSpacing: '0px', fontWeight: '400' }],
        'title-md': ['20px', { lineHeight: '26px', letterSpacing: '0.5px', fontWeight: '500' }],
        'title-lg': ['16px', { lineHeight: '24px', letterSpacing: '0.15px', fontWeight: '600' }],
        'title-medium': ['16px', { lineHeight: '24px', letterSpacing: '0.15px', fontWeight: '500' }],
        'title-small': ['14px', { lineHeight: '20px', letterSpacing: '0.1px', fontWeight: '500' }],
        // Label
        'label-large': ['14px', { lineHeight: '20px', letterSpacing: '0.1px', fontWeight: '500' }],
        'label-medium': ['12px', { lineHeight: '16px', letterSpacing: '0.5px', fontWeight: '500' }],
        'label-small': ['11px', { lineHeight: '16px', letterSpacing: '0.5px', fontWeight: '500' }],
        // Body
        'body-lg': ['18px', { lineHeight: '26px', letterSpacing: '0px', fontWeight: '400' }],
        'body-large': ['16px', { lineHeight: '24px', letterSpacing: '0.5px', fontWeight: '400' }],
        'body-medium': ['14px', { lineHeight: '20px', letterSpacing: '0.25px', fontWeight: '400' }],
        'body-small': ['12px', { lineHeight: '16px', letterSpacing: '0.4px', fontWeight: '400' }],
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    function({ addComponents }) {
      addComponents({
        '.container-fluid': {
          maxWidth: '1440px',
          width: '100%',
          marginLeft: 'auto',
          marginRight: 'auto',
        }
      })
    }
  ],
}
